@echo off
echo ========================================
echo    Google Maps Crawler - Windows
echo ========================================
echo.

REM Kiểm tra Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python không được cài đặt hoặc không có trong PATH
    echo Vui lòng cài đặt Python từ https://python.org
    pause
    exit /b 1
)

echo ✅ Python đã được cài đặt

REM Kiểm tra nếu đây là lần chạy đầu tiên
if not exist "venv" (
    echo.
    echo 🔧 Thiết lập lần đầu...
    echo Tạo virtual environment...
    python -m venv venv
    
    echo Kích hoạt virtual environment...
    call venv\Scripts\activate.bat
    
    echo Cài đặt dependencies...
    pip install -r requirements.txt
    
    echo Cài đặt Playwright browser...
    playwright install chromium
    
    echo ✅ Thiết lập hoàn tất!
) else (
    echo 🔧 Kích hoạt virtual environment...
    call venv\Scripts\activate.bat
)

echo.
echo 🚀 Sẵn sàng sử dụng!
echo.
echo Ví dụ sử dụng:
echo   python run_crawler.py "Language school"
echo   python run_crawler.py "Restaurant" --max-results 50 --get-details
echo   python test_crawler.py
echo.

REM Mở command prompt trong virtual environment
cmd /k
