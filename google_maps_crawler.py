import json
import time
import re
import urllib.parse
from playwright.sync_api import sync_playwright
from typing import List, Dict, Optional
import logging

# Thiết lập logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class GoogleMapsCrawler:
    def __init__(self, headless: bool = True, timeout: int = 60000):
        """
        Khởi tạo Google Maps Crawler

        Args:
            headless: Chạy browser ẩn hay hiển thị
            timeout: Thời gian timeout cho các thao tác (ms)
        """
        self.headless = headless
        self.timeout = timeout
        self.results = []
        
    def create_search_url(self, query: str, country_code: str = "vn", language: str = "vi") -> str:
        """
        Tạo URL tìm kiếm Google Maps với tham số vùng
        
        Args:
            query: Từ khóa tìm kiếm
            country_code: Mã quốc gia (vn cho Việt Nam)
            language: Ngôn ngữ hiển thị
            
        Returns:
            URL tìm kiếm hoàn chỉnh
        """
        encoded_query = urllib.parse.quote_plus(query)
        # Sử dụng tọa độ trung tâm Việt Nam để focus vào khu vực
        vietnam_center = "14.0583,108.2772,6z"
        url = f"https://www.google.com/maps/search/{encoded_query}/@{vietnam_center}?gl={country_code}&hl={language}"
        logger.info(f"Search URL: {url}")
        return url
        
    def wait_for_results_to_load(self, page, initial_wait: int = 3) -> None:
        """
        Chờ kết quả tìm kiếm load xong
        """
        time.sleep(initial_wait)
        try:
            # Chờ container kết quả xuất hiện
            page.wait_for_selector('div[role="feed"]', timeout=self.timeout)
            logger.info("Results container loaded successfully")
        except Exception as e:
            logger.warning(f"Could not find results container: {e}")
            
    def scroll_to_load_all_results(self, page, max_scrolls: int = 50, scroll_pause: float = 2) -> None:
        """
        Scroll để load tất cả kết quả (infinite scroll)
        
        Args:
            page: Playwright page object
            max_scrolls: Số lần scroll tối đa
            scroll_pause: Thời gian nghỉ giữa các lần scroll
        """
        logger.info("Starting to scroll for more results...")
        
        prev_height = 0
        scroll_count = 0
        no_change_count = 0
        
        while scroll_count < max_scrolls:
            try:
                # Scroll container kết quả xuống
                current_height = page.evaluate("""
                    () => {
                        const feed = document.querySelector('div[role="feed"]');
                        if (feed) {
                            feed.scrollBy(0, feed.scrollHeight);
                            return feed.scrollHeight;
                        }
                        return 0;
                    }
                """)
                
                time.sleep(scroll_pause)
                scroll_count += 1
                
                # Kiểm tra xem có load thêm content không
                if current_height == prev_height:
                    no_change_count += 1
                    if no_change_count >= 3:  # Nếu 3 lần liên tiếp không có thay đổi
                        logger.info("No more results to load")
                        break
                else:
                    no_change_count = 0
                    
                prev_height = current_height
                logger.info(f"Scroll {scroll_count}: Height = {current_height}")
                
            except Exception as e:
                logger.error(f"Error during scrolling: {e}")
                break
                
        logger.info(f"Completed scrolling after {scroll_count} attempts")
        
    def extract_place_id_from_url(self, url: str) -> Optional[str]:
        """
        Trích xuất place_id từ URL Google Maps
        """
        if not url:
            return None
            
        # Tìm place_id trong URL
        patterns = [
            r'place/([^/]+)',
            r'query_place_id=([^&]+)',
            r'data=([^&]+)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)
        return None
        
    def extract_business_info(self, page, element) -> Dict:
        """
        Trích xuất thông tin chi tiết từ một element kết quả
        """
        try:
            # Lấy thông tin cơ bản
            title = ""
            try:
                # Thử nhiều selector khác nhau cho title
                title_selectors = [
                    '[data-value="Title"]',
                    'div.qBF1Pd',
                    'div.NrDZNb',
                    'div.fontHeadlineSmall',
                    'span.fontHeadlineSmall',
                    'div[role="button"] span',
                    'div.fontBodyMedium span',
                    'h3',
                    'div:first-child span'
                ]

                for selector in title_selectors:
                    title_elem = element.query_selector(selector)
                    if title_elem:
                        title_text = title_elem.inner_text().strip()
                        if title_text and len(title_text) > 0:
                            title = title_text
                            break

                # Nếu vẫn không có, thử lấy từ aria-label
                if not title:
                    aria_label = element.get_attribute('aria-label')
                    if aria_label:
                        title = aria_label.split(',')[0].strip()

            except Exception as e:
                logger.debug(f"Error extracting title: {e}")
                pass
                
            # Lấy URL và place_id
            url = ""
            place_id = ""
            try:
                url = element.get_attribute('href') or ""
                place_id = self.extract_place_id_from_url(url)
            except:
                pass
                
            # Lấy địa chỉ
            address = ""
            try:
                # Thử nhiều selector cho địa chỉ
                addr_selectors = [
                    'span.W4Efsd:last-child',
                    '.W4Efsd',
                    'div.fontBodyMedium:last-child',
                    'span.fontBodyMedium',
                    'div:last-child span'
                ]

                for selector in addr_selectors:
                    addr_elem = element.query_selector(selector)
                    if addr_elem:
                        addr_text = addr_elem.inner_text().strip()
                        # Kiểm tra xem có phải địa chỉ không (chứa dấu phẩy hoặc từ khóa địa điểm)
                        if addr_text and (',' in addr_text or any(word in addr_text.lower() for word in ['quận', 'huyện', 'phường', 'đường', 'street', 'district'])):
                            address = addr_text
                            break
            except Exception as e:
                logger.debug(f"Error extracting address: {e}")
                pass

            # Lấy rating và số review
            rating = ""
            reviews_count = ""
            try:
                # Thử nhiều selector cho rating
                rating_selectors = ['span.MW4etd', 'span.fontBodyMedium', 'div[role="img"]']

                for selector in rating_selectors:
                    rating_elem = element.query_selector(selector)
                    if rating_elem:
                        rating_text = rating_elem.inner_text().strip()
                        # Kiểm tra xem có phải số rating không (1.0-5.0)
                        if rating_text and re.match(r'^[1-5]\.[0-9]$', rating_text):
                            rating = rating_text
                            break

                # Thử nhiều selector cho reviews count
                reviews_selectors = ['span.UY7F9', 'span:contains("(")', 'span.fontBodyMedium']

                for selector in reviews_selectors:
                    reviews_elem = element.query_selector(selector)
                    if reviews_elem:
                        reviews_text = reviews_elem.inner_text().strip()
                        # Trích xuất số từ text như "(324)" hoặc "324 reviews"
                        reviews_match = re.search(r'\(?(\d+)\)?', reviews_text)
                        if reviews_match:
                            reviews_count = reviews_match.group(1)
                            break

            except Exception as e:
                logger.debug(f"Error extracting rating/reviews: {e}")
                pass

            # Lấy category
            category = ""
            try:
                # Thử nhiều selector cho category
                cat_selectors = [
                    'span.W4Efsd:first-child',
                    'div.fontBodyMedium:first-child',
                    'span.fontBodyMedium:first-child'
                ]

                for selector in cat_selectors:
                    cat_elem = element.query_selector(selector)
                    if cat_elem:
                        cat_text = cat_elem.inner_text().strip()
                        # Kiểm tra xem có phải category không (không chứa số và dấu phẩy)
                        if cat_text and not re.search(r'\d|,', cat_text) and len(cat_text) < 50:
                            category = cat_text
                            break
            except Exception as e:
                logger.debug(f"Error extracting category: {e}")
                pass
                
            # Parse địa chỉ thành các component
            street, city, state = self.parse_address(address)
            
            result = {
                "title": title,
                "phone": "",  # Sẽ cần click vào để lấy
                "website": "",  # Sẽ cần click vào để lấy
                "email": "",
                "totalScore": float(rating) if rating and rating.replace('.', '').isdigit() else 0,
                "reviewsCount": int(reviews_count) if reviews_count.isdigit() else 0,
                "street": street,
                "city": city,
                "state": state,
                "countryCode": "VN",
                "categoryName": category,
                "url": url,
                "formatted_address": address,
                "place_id": place_id or ""
            }
            
            return result
            
        except Exception as e:
            logger.error(f"Error extracting business info: {e}")
            return {}
            
    def parse_address(self, address: str) -> tuple:
        """
        Parse địa chỉ thành street, city, state
        """
        if not address:
            return "", "", ""
            
        parts = [part.strip() for part in address.split(',')]
        
        street = parts[0] if len(parts) > 0 else ""
        city = parts[1] if len(parts) > 1 else ""
        state = parts[2] if len(parts) > 2 else ""
        
        return street, city, state

    def get_detailed_info(self, page, result: Dict) -> Dict:
        """
        Click vào một kết quả để lấy thông tin chi tiết (phone, website, rating, address)
        """
        try:
            if not result.get('url'):
                return result

            # Click vào kết quả để mở panel chi tiết
            element = page.query_selector(f'a[href*="{result.get("place_id", "")}"]')
            if not element:
                # Thử tìm bằng title
                elements = page.query_selector_all('a[href*="/place/"]')
                for elem in elements:
                    if result.get('title', '').lower() in elem.inner_text().lower():
                        element = elem
                        break

            if element:
                element.click()
                time.sleep(3)  # Chờ panel load

                # Lấy rating và reviews từ panel chi tiết
                try:
                    rating_elem = page.query_selector('div.F7nice')
                    if rating_elem:
                        rating_text = rating_elem.inner_text().strip()
                        lines = rating_text.split('\n')
                        if len(lines) >= 2:
                            # Dòng đầu là rating, dòng thứ 2 là số reviews
                            rating = lines[0].replace(',', '.')
                            reviews_text = lines[1]

                            # Parse rating
                            if rating and re.match(r'^[1-5]\.[0-9]$', rating):
                                result['totalScore'] = float(rating)

                            # Parse reviews count
                            reviews_match = re.search(r'\((\d+)\)', reviews_text)
                            if reviews_match:
                                result['reviewsCount'] = int(reviews_match.group(1))
                except Exception as e:
                    logger.debug(f"Error extracting rating: {e}")

                # Lấy địa chỉ từ panel chi tiết
                try:
                    addr_elem = page.query_selector('button[data-value="Address"]')
                    if addr_elem:
                        addr_text = addr_elem.get_attribute('aria-label', '')
                        if addr_text:
                            # Loại bỏ "Address: " prefix
                            address = addr_text.replace('Address: ', '').strip()
                            result['formatted_address'] = address

                            # Parse địa chỉ thành components
                            street, city, state = self.parse_address(address)
                            result['street'] = street
                            result['city'] = city
                            result['state'] = state
                except Exception as e:
                    logger.debug(f"Error extracting address: {e}")

                # Lấy số điện thoại
                try:
                    phone_selectors = [
                        'button[data-value="Phone number"]',
                        'button[aria-label*="phone"]',
                        'button[aria-label*="Phone"]'
                    ]

                    for selector in phone_selectors:
                        phone_elem = page.query_selector(selector)
                        if phone_elem:
                            phone_text = phone_elem.get_attribute('aria-label', '')
                            if phone_text:
                                # Loại bỏ prefix và lấy số điện thoại
                                phone = phone_text.replace('Phone: ', '').replace('Call ', '').strip()
                                if phone:
                                    result['phone'] = phone
                                    break
                except Exception as e:
                    logger.debug(f"Error extracting phone: {e}")

                # Lấy website
                try:
                    website_selectors = [
                        'a[data-value="Website"]',
                        'a[aria-label*="Website"]',
                        'a[aria-label*="website"]'
                    ]

                    for selector in website_selectors:
                        website_elem = page.query_selector(selector)
                        if website_elem:
                            website_url = website_elem.get_attribute('href', '')
                            if website_url and website_url.startswith('http'):
                                result['website'] = website_url
                                break
                except Exception as e:
                    logger.debug(f"Error extracting website: {e}")

        except Exception as e:
            logger.error(f"Error getting detailed info: {e}")

        return result

    def crawl_search_results(self, query: str, get_details: bool = False, max_results: int = None) -> List[Dict]:
        """
        Crawl kết quả tìm kiếm từ Google Maps

        Args:
            query: Từ khóa tìm kiếm
            get_details: Có lấy thông tin chi tiết (phone, website) không
            max_results: Giới hạn số kết quả (None = không giới hạn)

        Returns:
            List các kết quả dưới dạng dictionary
        """
        logger.info(f"Starting crawl for query: '{query}'")

        with sync_playwright() as p:
            # Khởi tạo browser
            browser = p.chromium.launch(
                headless=self.headless,
                args=[
                    '--no-sandbox',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-dev-shm-usage'
                ]
            )

            # Tạo context với user agent thật
            context = browser.new_context(
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                viewport={'width': 1920, 'height': 1080}
            )

            page = context.new_page()

            try:
                # Tạo URL tìm kiếm và truy cập
                search_url = self.create_search_url(query)
                logger.info(f"Navigating to: {search_url}")
                page.goto(search_url, wait_until='domcontentloaded', timeout=self.timeout)

                # Chờ kết quả load
                self.wait_for_results_to_load(page)

                # Scroll để load tất cả kết quả
                self.scroll_to_load_all_results(page)

                # Tìm tất cả các kết quả
                logger.info("Extracting results...")

                # Thử nhiều selector khác nhau
                selectors_to_try = [
                    'a[href*="/place/"]',
                    'div[role="feed"] a[href*="/place/"]',
                    'div[role="feed"] > div > div > a',
                    'div[role="feed"] a',
                    '[data-result-index] a'
                ]

                result_elements = []
                for selector in selectors_to_try:
                    result_elements = page.query_selector_all(selector)
                    if result_elements:
                        logger.info(f"Found results using selector: {selector}")
                        break

                logger.info(f"Found {len(result_elements)} results")

                results = []
                for i, element in enumerate(result_elements):
                    if max_results and i >= max_results:
                        break

                    logger.info(f"Processing result {i+1}/{len(result_elements)}")

                    # Trích xuất thông tin cơ bản
                    result = self.extract_business_info(page, element)

                    if result and result.get('title'):
                        # Lấy thông tin chi tiết nếu được yêu cầu
                        if get_details:
                            result = self.get_detailed_info(page, result)

                        results.append(result)
                        logger.info(f"Extracted: {result.get('title', 'Unknown')}")

                self.results = results
                logger.info(f"Crawling completed. Total results: {len(results)}")

            except Exception as e:
                logger.error(f"Error during crawling: {e}")

            finally:
                browser.close()

        return self.results

    def save_to_json(self, filename: str = None) -> str:
        """
        Lưu kết quả ra file JSON

        Args:
            filename: Tên file (nếu None sẽ tự động tạo)

        Returns:
            Đường dẫn file đã lưu
        """
        if not filename:
            timestamp = int(time.time())
            filename = f"google_maps_results_{timestamp}.json"

        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)

        logger.info(f"Results saved to: {filename}")
        return filename

    def print_summary(self) -> None:
        """
        In tóm tắt kết quả
        """
        if not self.results:
            print("No results found.")
            return

        print(f"\n=== CRAWL SUMMARY ===")
        print(f"Total results: {len(self.results)}")
        print(f"Results with phone: {sum(1 for r in self.results if r.get('phone'))}")
        print(f"Results with website: {sum(1 for r in self.results if r.get('website'))}")
        print(f"Results with rating: {sum(1 for r in self.results if r.get('totalScore', 0) > 0)}")

        print(f"\n=== SAMPLE RESULTS ===")
        for i, result in enumerate(self.results[:3]):  # Hiển thị 3 kết quả đầu
            print(f"\n{i+1}. {result.get('title', 'N/A')}")
            print(f"   Address: {result.get('formatted_address', 'N/A')}")
            print(f"   Rating: {result.get('totalScore', 'N/A')} ({result.get('reviewsCount', 0)} reviews)")
            print(f"   Phone: {result.get('phone', 'N/A')}")
            print(f"   Website: {result.get('website', 'N/A')}")


def main():
    """
    Hàm main để test crawler
    """
    # Khởi tạo crawler
    crawler = GoogleMapsCrawler(headless=False)  # headless=False để xem quá trình

    # Tìm kiếm
    query = "Language school"
    results = crawler.crawl_search_results(
        query=query,
        get_details=True,  # Lấy thông tin chi tiết
        max_results=20     # Giới hạn 20 kết quả đầu
    )

    # In tóm tắt
    crawler.print_summary()

    # Lưu file
    filename = crawler.save_to_json()
    print(f"\nResults saved to: {filename}")


if __name__ == "__main__":
    main()
