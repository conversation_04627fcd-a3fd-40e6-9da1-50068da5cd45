# 🚀 Hướng dẫn nhanh - Google Maps Crawler

## Cài đặt nhanh (Windows)

1. **Mở Command Prompt** tại thư mục dự án
2. **Cài đặt dependencies:**
   ```bash
   pip install playwright requests
   python -m playwright install chromium
   ```

## Sử dụng ngay

### 1. Chạy demo với từ khóa "Language school"
```bash
python demo.py
```

### 2. Chạy với từ khóa tùy chỉnh
```bash
python run_crawler.py "Restaurant" --max-results 20 --get-details
```

### 3. Test nhanh
```bash
python quick_test.py
```

## Kết quả

Crawler sẽ tạo file JSON với format:
```json
[
  {
    "title": "Apollo English Centre",
    "phone": "+84 24 3974 4157",
    "website": "https://apollo.edu.vn",
    "email": "",
    "totalScore": 4.2,
    "reviewsCount": 324,
    "street": "127 Nguyễn <PERSON>",
    "city": "Quận Hai Bà Trưng",
    "state": "Hà Nội",
    "countryCode": "VN",
    "categoryName": "Trung tâm tiếng Anh",
    "url": "https://www.google.com/maps/place/...",
    "formatted_address": "127 Nguyễn Huệ, Quận Hai Bà Trưng, Hà Nội, Việt Nam",
    "place_id": "ChIJAyXdEfFbNDERg8AiLELrVBz"
  }
]
```

## Tính năng chính

✅ **Tìm kiếm theo từ khóa** với focus vào Việt Nam  
✅ **Infinite scroll** để lấy tất cả kết quả  
✅ **Lấy thông tin chi tiết**: tên, địa chỉ, rating, reviews  
✅ **Xuất JSON** theo format yêu cầu  
✅ **Hoàn toàn miễn phí** - không cần API key  

## Lưu ý

- Crawler sẽ mở browser để thực hiện (có thể đặt `headless=True` để ẩn)
- Thời gian crawl phụ thuộc vào số lượng kết quả
- Google Maps có thể thay đổi cấu trúc HTML, cần cập nhật selectors

## Hỗ trợ

Nếu gặp lỗi, hãy chạy:
```bash
python simple_test.py
```

Để debug cấu trúc HTML:
```bash
python debug_selectors.py
```
