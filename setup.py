#!/usr/bin/env python3
"""
Script setup để cài đặt dependencies và Playwright browsers
"""

import subprocess
import sys
import os

def run_command(command, description):
    """Chạy command và hiển thị kết quả"""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} thành công!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} thất bại!")
        print(f"Error: {e.stderr}")
        return False

def main():
    print("🚀 Thiết lập Google Maps Crawler")
    print("=" * 40)
    
    # Kiểm tra Python version
    if sys.version_info < (3, 7):
        print("❌ Cần Python 3.7 trở lên!")
        sys.exit(1)
    
    print(f"✅ Python version: {sys.version}")
    
    # Cài đặt dependencies
    if not run_command("pip install -r requirements.txt", "Cài đặt Python packages"):
        sys.exit(1)
    
    # Cài đặt Playwright browsers
    if not run_command("playwright install chromium", "Cài đặt Chromium browser"):
        sys.exit(1)
    
    print("\n🎉 Thiết lập hoàn tất!")
    print("\n📖 Cách sử dụng:")
    print("python run_crawler.py \"Language school\"")
    print("python run_crawler.py \"Restaurant\" --max-results 100 --get-details")

if __name__ == "__main__":
    main()
