#!/usr/bin/env python3
"""
Test nhanh crawler với ít kết quả
"""

from google_maps_crawler import GoogleMapsCrawler

def main():
    print("🧪 Test nhanh Google Maps Crawler")
    print("-" * 40)
    
    # Khởi tạo crawler (hiển thị browser để xem)
    crawler = GoogleMapsCrawler(headless=False, timeout=60000)
    
    # Test với từ khóa đơn giản
    query = "Coffee shop"
    print(f"🔍 Tìm kiếm: '{query}'")
    
    results = crawler.crawl_search_results(
        query=query,
        get_details=True,   # Lấy chi tiết để test đầy đủ
        max_results=3       # Chỉ 3 kết quả để test nhanh hơn
    )
    
    print(f"\n📊 Kết quả: {len(results)} địa điểm")
    
    if results:
        print("\n📋 Danh sách:")
        for i, result in enumerate(results, 1):
            print(f"{i}. {result.get('title', 'N/A')}")
            print(f"   📍 {result.get('formatted_address', 'N/A')}")
            print(f"   ⭐ {result.get('totalScore', 'N/A')} ({result.get('reviewsCount', 0)} reviews)")
            print()
        
        # Lưu file
        filename = crawler.save_to_json("quick_test_results.json")
        print(f"💾 Đã lưu: {filename}")
    else:
        print("❌ Không có kết quả")

if __name__ == "__main__":
    main()
