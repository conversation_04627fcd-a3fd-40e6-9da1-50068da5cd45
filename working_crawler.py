#!/usr/bin/env python3
"""
<PERSON>ript thực sự hoạt động - Sử dụng ĐÚNG selector của người dùng
"""

from playwright.sync_api import sync_playwright
import time
import json
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_business_details(page, business_name):
    """
    Lấy thông tin chi tiết từ panel sử dụng ĐÚNG selector
    """
    result = {
        "title": business_name,
        "phone": "",
        "website": "",
        "email": "",
        "totalScore": 0,
        "reviewsCount": 0,
        "street": "",
        "city": "",
        "state": "",
        "countryCode": "VN",
        "categoryName": "",
        "url": "",
        "formatted_address": "",
        "place_id": business_name.replace(' ', '+')
    }
    
    try:
        # Chờ panel load hoàn toàn
        time.sleep(8)
        
        # 1. <PERSON><PERSON><PERSON> tên từ selector của bạn
        title_selector = "h1.DUwDvf.lfPIob"
        title_elem = page.query_selector(title_selector)
        if title_elem:
            title = title_elem.inner_text().strip()
            result["title"] = title
            logger.info(f"✅ Title: {title}")
        
        # 2. Lấy địa chỉ từ selector của bạn
        address_selector = "#QA0Szd > div > div > div.w6VYqd > div.bJzME.Hu9e2e.tTVLSc > div > div.e07Vkf.kA9KIf > div > div > div.m6QErb.DxyBCb.kA9KIf.dS8AEf.XiKgde > div:nth-child(7) > div:nth-child(3) > button > div > div.rogA2c > div.Io6YTe.fontBodyMedium.kR99db.fdkmkc"
        address_elem = page.query_selector(address_selector)
        if address_elem:
            address = address_elem.inner_text().strip()
            result["formatted_address"] = address
            # Parse địa chỉ
            parts = address.split(',')
            if len(parts) >= 1:
                result["street"] = parts[0].strip()
            if len(parts) >= 2:
                result["city"] = parts[1].strip()
            if len(parts) >= 3:
                result["state"] = parts[2].strip()
            logger.info(f"✅ Address: {address}")
        
        # 3. Lấy website từ selector của bạn
        website_selector = "#QA0Szd > div > div > div.w6VYqd > div.bJzME.Hu9e2e.tTVLSc > div > div.e07Vkf.kA9KIf > div > div > div.m6QErb.DxyBCb.kA9KIf.dS8AEf.XiKgde > div:nth-child(7) > div:nth-child(6) > a > div > div.rogA2c.ITvuef > div.Io6YTe.fontBodyMedium.kR99db.fdkmkc"
        website_elem = page.query_selector(website_selector)
        if website_elem:
            website = website_elem.inner_text().strip()
            if website and not website.startswith('http'):
                website = 'https://' + website
            result["website"] = website
            logger.info(f"✅ Website: {website}")
        else:
            # Thử lấy href từ link cha
            website_link_selector = "#QA0Szd > div > div > div.w6VYqd > div.bJzME.Hu9e2e.tTVLSc > div > div.e07Vkf.kA9KIf > div > div > div.m6QErb.DxyBCb.kA9KIf.dS8AEf.XiKgde > div:nth-child(7) > div:nth-child(6) > a"
            website_link = page.query_selector(website_link_selector)
            if website_link:
                href = website_link.get_attribute('href')
                if href and href.startswith('http'):
                    result["website"] = href
                    logger.info(f"✅ Website (href): {href}")
        
        # 4. Lấy số điện thoại từ selector của bạn
        phone_selector = "#QA0Szd > div > div > div.w6VYqd > div.bJzME.Hu9e2e.tTVLSc > div > div.e07Vkf.kA9KIf > div > div > div.m6QErb.DxyBCb.kA9KIf.dS8AEf.XiKgde > div:nth-child(7) > div:nth-child(7) > button > div > div.rogA2c > div.Io6YTe.fontBodyMedium.kR99db.fdkmkc"
        phone_elem = page.query_selector(phone_selector)
        if phone_elem:
            phone = phone_elem.inner_text().strip()
            result["phone"] = phone
            logger.info(f"✅ Phone: {phone}")
        
        # 5. Lấy rating từ selector của bạn
        rating_selector = "#QA0Szd > div > div > div.w6VYqd > div.bJzME.Hu9e2e.tTVLSc > div > div.e07Vkf.kA9KIf > div > div > div.m6QErb.DxyBCb.kA9KIf.dS8AEf.XiKgde > div.TIHn2 > div > div.lMbq3e > div.LBgpqf > div > div.fontBodyMedium.dmRWX > div.F7nice > span:nth-child(1) > span:nth-child(1)"
        rating_elem = page.query_selector(rating_selector)
        if rating_elem:
            rating_text = rating_elem.inner_text().strip().replace(',', '.')
            try:
                rating = float(rating_text)
                result["totalScore"] = rating
                logger.info(f"✅ Rating: {rating}")
            except:
                pass
        
        # 6. Lấy số reviews từ selector của bạn
        reviews_selector = "#QA0Szd > div > div > div.w6VYqd > div.bJzME.Hu9e2e.tTVLSc > div > div.e07Vkf.kA9KIf > div > div > div.m6QErb.DxyBCb.kA9KIf.dS8AEf.XiKgde > div.TIHn2 > div > div.lMbq3e > div.LBgpqf > div > div.fontBodyMedium.dmRWX > div.F7nice > span:nth-child(2) > span > span"
        reviews_elem = page.query_selector(reviews_selector)
        if reviews_elem:
            reviews_text = reviews_elem.inner_text().strip()
            try:
                # Loại bỏ dấu phẩy và ký tự không phải số
                reviews_clean = ''.join(filter(str.isdigit, reviews_text))
                if reviews_clean:
                    reviews = int(reviews_clean)
                    result["reviewsCount"] = reviews
                    logger.info(f"✅ Reviews: {reviews}")
            except:
                pass
        
        return result
        
    except Exception as e:
        logger.error(f"❌ Error getting details: {e}")
        return result

def crawl_language_schools():
    """
    Crawl trung tâm ngoại ngữ với selector chính xác
    """
    print("🎯 Crawl Language Schools với selector chính xác")
    print("=" * 60)
    
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)  # Hiển thị để debug
        context = browser.new_context(
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        )
        page = context.new_page()
        
        try:
            # Truy cập Google Maps
            search_url = "https://www.google.com/maps/search/Language+school/@14.0583,108.2772,6z?gl=vn&hl=vi"
            logger.info(f"🔍 Truy cập: {search_url}")
            page.goto(search_url, timeout=60000)
            time.sleep(5)
            
            # Chờ kết quả load
            page.wait_for_selector('div[role="feed"]', timeout=30000)
            
            # Lấy danh sách kết quả
            # Sử dụng selector của bạn để click vào từng kết quả
            result_selector = "#QA0Szd > div > div > div.w6VYqd > div:nth-child(2) > div > div.e07Vkf.kA9KIf > div > div > div.m6QErb.DxyBCb.kA9KIf.dS8AEf.XiKgde.ecceSd > div.m6QErb.DxyBCb.kA9KIf.dS8AEf.XiKgde.ecceSd > div:nth-child(3) > div > a"
            
            # Thử selector đơn giản hơn trước
            results = page.query_selector_all('a[href*="/place/"]')
            logger.info(f"📊 Tìm thấy {len(results)} kết quả")
            
            if not results:
                logger.error("❌ Không tìm thấy kết quả nào!")
                return []
            
            all_results = []
            max_results = 5  # Test với 5 kết quả đầu
            
            for i, result_link in enumerate(results[:max_results]):
                try:
                    # Lấy tên business từ link
                    business_name = result_link.inner_text().strip().split('\n')[0]
                    if not business_name or len(business_name) < 3:
                        continue
                    
                    logger.info(f"\n🎯 Processing {i+1}/{max_results}: {business_name}")
                    
                    # Click vào kết quả
                    result_link.click()
                    
                    # Lấy thông tin chi tiết
                    details = get_business_details(page, business_name)
                    all_results.append(details)
                    
                    # Log kết quả
                    print(f"\n📋 Kết quả {i+1}:")
                    print(f"   📝 Tên: {details['title']}")
                    print(f"   📞 Phone: {details['phone'] or 'Không có'}")
                    print(f"   🌐 Website: {details['website'] or 'Không có'}")
                    print(f"   📍 Address: {details['formatted_address'] or 'Không có'}")
                    print(f"   ⭐ Rating: {details['totalScore']} ({details['reviewsCount']} reviews)")
                    
                    # Chờ trước khi chuyển sang kết quả tiếp theo
                    time.sleep(3)
                    
                except Exception as e:
                    logger.error(f"❌ Error processing result {i+1}: {e}")
                    continue
            
            # Lưu kết quả
            filename = "working_crawler_results.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(all_results, f, ensure_ascii=False, indent=2)
            
            # Thống kê
            with_website = sum(1 for r in all_results if r['website'])
            with_phone = sum(1 for r in all_results if r['phone'])
            with_address = sum(1 for r in all_results if r['formatted_address'])
            
            print(f"\n🎉 HOÀN THÀNH!")
            print(f"📊 Tổng kết quả: {len(all_results)}")
            print(f"🌐 Có website: {with_website}/{len(all_results)}")
            print(f"📞 Có phone: {with_phone}/{len(all_results)}")
            print(f"📍 Có address: {with_address}/{len(all_results)}")
            print(f"💾 Đã lưu vào: {filename}")
            
            return all_results
            
        except Exception as e:
            logger.error(f"❌ Lỗi chính: {e}")
            return []
            
        finally:
            input("\nNhấn Enter để đóng browser...")
            browser.close()

if __name__ == "__main__":
    crawl_language_schools()
