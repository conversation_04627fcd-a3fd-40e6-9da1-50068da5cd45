#!/usr/bin/env python3
"""
Test đơn giản - Chỉ click 1 kết quả và lấy thông tin
"""

from playwright.sync_api import sync_playwright
import time

def simple_test():
    print("🧪 Simple Test - Click 1 kết quả và lấy thông tin")
    print("=" * 50)

    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        page = browser.new_page()

        try:
            # Truy cập Google Maps
            url = "https://www.google.com/maps/search/Language+school/@14.0583,108.2772,6z?gl=vn&hl=vi"
            print(f"📍 Truy cập: {url}")
            page.goto(url, timeout=60000)
            time.sleep(5)

            # Tìm kết quả đầu tiên
            results = page.query_selector_all('a[href*="/place/"]')
            print(f"📊 Tìm thấy {len(results)} kết quả")

            if results:
                first_result = results[0]

                # Lấy text của kết quả đầu tiên
                result_text = first_result.inner_text()
                print(f"📝 Text kết quả đầu tiên:")
                print(result_text[:200] + "...")

                # Click vào kết quả đầu tiên
                print(f"\n🖱️  Click vào kết quả đầu tiên...")
                first_result.click()

                # Chờ panel load
                print("⏳ Chờ 10 giây để panel load...")
                time.sleep(10)

                print(f"\n🔍 Test các selector của bạn:")

                # Test từng selector một
                selectors = [
                    ("Tên", "h1.DUwDvf.lfPIob"),
                    ("Địa chỉ", "#QA0Szd > div > div > div.w6VYqd > div.bJzME.Hu9e2e.tTVLSc > div > div.e07Vkf.kA9KIf > div > div > div.m6QErb.DxyBCb.kA9KIf.dS8AEf.XiKgde > div:nth-child(7) > div:nth-child(3) > button > div > div.rogA2c > div.Io6YTe.fontBodyMedium.kR99db.fdkmkc"),
                    ("Website", "#QA0Szd > div > div > div.w6VYqd > div.bJzME.Hu9e2e.tTVLSc > div > div.e07Vkf.kA9KIf > div > div > div.m6QErb.DxyBCb.kA9KIf.dS8AEf.XiKgde > div:nth-child(7) > div:nth-child(6) > a > div > div.rogA2c.ITvuef > div.Io6YTe.fontBodyMedium.kR99db.fdkmkc"),
                    ("Điện thoại", "#QA0Szd > div > div > div.w6VYqd > div.bJzME.Hu9e2e.tTVLSc > div > div.e07Vkf.kA9KIf > div > div > div.m6QErb.DxyBCb.kA9KIf.dS8AEf.XiKgde > div:nth-child(7) > div:nth-child(7) > button > div > div.rogA2c > div.Io6YTe.fontBodyMedium.kR99db.fdkmkc"),
                    ("Rating", "#QA0Szd > div > div > div.w6VYqd > div.bJzME.Hu9e2e.tTVLSc > div > div.e07Vkf.kA9KIf > div > div > div.m6QErb.DxyBCb.kA9KIf.dS8AEf.XiKgde > div.TIHn2 > div > div.lMbq3e > div.LBgpqf > div > div.fontBodyMedium.dmRWX > div.F7nice > span:nth-child(1) > span:nth-child(1)"),
                    ("Reviews", "#QA0Szd > div > div > div.w6VYqd > div.bJzME.Hu9e2e.tTVLSc > div > div.e07Vkf.kA9KIf > div > div > div.m6QErb.DxyBCb.kA9KIf.dS8AEf.XiKgde > div.TIHn2 > div > div.lMbq3e > div.LBgpqf > div > div.fontBodyMedium.dmRWX > div.F7nice > span:nth-child(2) > span > span")
                ]

                results_found = {}

                for name, selector in selectors:
                    try:
                        elem = page.query_selector(selector)
                        if elem:
                            text = elem.inner_text().strip()
                            results_found[name] = text
                            print(f"✅ {name}: '{text}'")
                        else:
                            results_found[name] = None
                            print(f"❌ {name}: Không tìm thấy")
                    except Exception as e:
                        results_found[name] = None
                        print(f"⚠️  {name}: Lỗi - {e}")

                # Tạo JSON kết quả
                final_result = {
                    "title": results_found.get("Tên", ""),
                    "phone": results_found.get("Điện thoại", ""),
                    "website": results_found.get("Website", ""),
                    "email": "",
                    "totalScore": float(results_found.get("Rating", "0").replace(",", ".")) if results_found.get("Rating") else 0,
                    "reviewsCount": int(''.join(filter(str.isdigit, results_found.get("Reviews", "0")))) if results_found.get("Reviews") else 0,
                    "street": "",
                    "city": "",
                    "state": "",
                    "countryCode": "VN",
                    "categoryName": "",
                    "url": "",
                    "formatted_address": results_found.get("Địa chỉ", ""),
                    "place_id": ""
                }

                # Parse địa chỉ
                if final_result["formatted_address"]:
                    parts = final_result["formatted_address"].split(",")
                    if len(parts) >= 1:
                        final_result["street"] = parts[0].strip()
                    if len(parts) >= 2:
                        final_result["city"] = parts[1].strip()
                    if len(parts) >= 3:
                        final_result["state"] = parts[2].strip()

                # Đảm bảo website có protocol
                if final_result["website"] and not final_result["website"].startswith("http"):
                    final_result["website"] = "https://" + final_result["website"]

                print(f"\n📋 KẾT QUẢ CUỐI CÙNG:")
                print(f"📝 Tên: {final_result['title']}")
                print(f"📞 Phone: {final_result['phone']}")
                print(f"🌐 Website: {final_result['website']}")
                print(f"📍 Address: {final_result['formatted_address']}")
                print(f"⭐ Rating: {final_result['totalScore']} ({final_result['reviewsCount']} reviews)")

                # Lưu kết quả
                import json
                with open('simple_test_result.json', 'w', encoding='utf-8') as f:
                    json.dump(final_result, f, ensure_ascii=False, indent=2)

                print(f"\n💾 Đã lưu kết quả vào: simple_test_result.json")

                # Kiểm tra thành công
                success_count = sum(1 for key in ['title', 'phone', 'website', 'formatted_address'] if final_result[key])
                print(f"\n🎯 Thành công lấy được {success_count}/4 thông tin chính")

                if final_result['website']:
                    print("🎉 ĐÃ LẤY ĐƯỢC WEBSITE!")
                else:
                    print("❌ Chưa lấy được website")

            else:
                print("❌ Không tìm thấy kết quả nào!")

        except Exception as e:
            print(f"❌ Lỗi: {e}")

        finally:
            input("\nNhấn Enter để đóng browser...")
            browser.close()

if __name__ == "__main__":
    simple_test()
