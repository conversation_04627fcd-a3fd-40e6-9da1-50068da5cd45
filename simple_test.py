#!/usr/bin/env python3
"""
Test đơn giản để kiểm tra Playwright và Google Maps
"""

from playwright.sync_api import sync_playwright
import time

def test_basic_connection():
    """Test kết nối cơ bản đến Google Maps"""
    print("🧪 Test kết nối cơ bản đến Google Maps...")
    
    with sync_playwright() as p:
        # Khởi tạo browser
        browser = p.chromium.launch(headless=False)  # Hiển thị để xem
        context = browser.new_context(
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        )
        page = context.new_page()
        
        try:
            # Truy cập Google Maps
            print("📍 Đang truy cập Google Maps...")
            page.goto("https://www.google.com/maps", timeout=60000)
            
            # Chờ trang load
            time.sleep(3)
            
            # Kiểm tra title
            title = page.title()
            print(f"✅ Trang đã load: {title}")
            
            # Tìm search box
            search_box = page.query_selector('input[id="searchboxinput"]')
            if search_box:
                print("✅ Tìm thấy search box")
                
                # Thử tìm kiếm
                search_box.fill("Coffee shop Vietnam")
                search_box.press("Enter")
                
                print("🔍 Đã gửi tìm kiếm, chờ kết quả...")
                time.sleep(5)
                
                # Kiểm tra có kết quả không
                results = page.query_selector_all('div[role="feed"] a')
                print(f"📊 Tìm thấy {len(results)} kết quả")
                
                if results:
                    # Lấy thông tin kết quả đầu tiên
                    first_result = results[0]
                    try:
                        title_elem = first_result.query_selector('div.qBF1Pd')
                        if title_elem:
                            title = title_elem.inner_text()
                            print(f"🏪 Kết quả đầu tiên: {title}")
                    except:
                        print("⚠️  Không thể lấy title của kết quả đầu tiên")
                
            else:
                print("❌ Không tìm thấy search box")
                
        except Exception as e:
            print(f"❌ Lỗi: {e}")
            
        finally:
            print("🔄 Đóng browser...")
            browser.close()

def test_search_url():
    """Test URL tìm kiếm trực tiếp"""
    print("\n🧪 Test URL tìm kiếm trực tiếp...")
    
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        context = browser.new_context(
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        )
        page = context.new_page()
        
        try:
            # URL tìm kiếm trực tiếp
            search_url = "https://www.google.com/maps/search/Coffee+shop/@14.0583,108.2772,6z?gl=vn&hl=vi"
            print(f"📍 Truy cập: {search_url}")
            
            page.goto(search_url, timeout=60000)
            time.sleep(5)
            
            # Kiểm tra kết quả
            results = page.query_selector_all('a[href*="/place/"]')
            print(f"📊 Tìm thấy {len(results)} kết quả")
            
            if results:
                print("✅ Tìm kiếm thành công!")
                
                # Lấy thông tin một vài kết quả đầu
                for i, result in enumerate(results[:3]):
                    try:
                        # Thử các selector khác nhau
                        title = ""
                        selectors = ['div.qBF1Pd', 'div.NrDZNb', '[data-value="Title"]']
                        
                        for selector in selectors:
                            title_elem = result.query_selector(selector)
                            if title_elem:
                                title = title_elem.inner_text().strip()
                                break
                        
                        if title:
                            print(f"  {i+1}. {title}")
                        else:
                            print(f"  {i+1}. (Không lấy được tên)")
                            
                    except Exception as e:
                        print(f"  {i+1}. Lỗi: {e}")
            else:
                print("❌ Không tìm thấy kết quả")
                
        except Exception as e:
            print(f"❌ Lỗi: {e}")
            
        finally:
            browser.close()

if __name__ == "__main__":
    print("🚀 Bắt đầu test Google Maps Crawler")
    print("=" * 50)
    
    # Test 1: Kết nối cơ bản
    test_basic_connection()
    
    # Test 2: URL tìm kiếm trực tiếp
    test_search_url()
    
    print("\n🎉 Test hoàn thành!")
