#!/usr/bin/env python3
"""
Test thủ công từng bước để debug
"""

from playwright.sync_api import sync_playwright
import time
import re

def manual_test():
    """Test thủ công từng bước"""
    print("🔧 Test thủ công từng bước")
    print("=" * 40)
    
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        context = browser.new_context(
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        )
        page = context.new_page()
        
        try:
            # Bước 1: T<PERSON><PERSON> cập Google Maps
            search_url = "https://www.google.com/maps/search/Language+school/@14.0583,108.2772,6z?gl=vn&hl=vi"
            print(f"📍 Bước 1: <PERSON><PERSON><PERSON> cậ<PERSON> {search_url}")
            page.goto(search_url, timeout=60000)
            time.sleep(5)
            
            # Bước 2: <PERSON><PERSON><PERSON> kết quả
            results = page.query_selector_all('a[href*="/place/"]')
            print(f"📊 Bước 2: Tìm thấy {len(results)} kết quả")
            
            if results:
                # Bước 3: Click vào kết quả đầu tiên
                first_result = results[0]
                result_title = first_result.inner_text().strip()
                print(f"🎯 Bước 3: Click vào '{result_title}'")
                first_result.click()
                
                # Bước 4: Chờ panel load
                print("⏳ Bước 4: Chờ panel chi tiết load...")
                time.sleep(8)
                
                # Bước 5: Lấy thông tin cơ bản
                print("📋 Bước 5: Lấy thông tin cơ bản")
                
                # Tên
                title_elem = page.query_selector('h1.DUwDvf.lfPIob')
                title = title_elem.inner_text().strip() if title_elem else "N/A"
                print(f"   📝 Tên: {title}")
                
                # Bước 6: Tìm website
                print("🌐 Bước 6: Tìm website...")
                all_links = page.query_selector_all('a')
                website = None
                
                for link in all_links[:20]:  # Chỉ check 20 links đầu
                    try:
                        href = link.get_attribute('href') or ''
                        text = link.inner_text().strip()
                        
                        if (href and href.startswith('http') and 
                            'google.com' not in href and 
                            'gstatic.com' not in href):
                            
                            print(f"   🔗 Found link: {href} (text: '{text[:30]}...')")
                            
                            if 'trang web' in text.lower() or 'website' in text.lower():
                                website = href
                                print(f"   ✅ Website found: {website}")
                                break
                    except:
                        continue
                
                if not website:
                    # Lấy link đầu tiên không phải Google
                    for link in all_links:
                        try:
                            href = link.get_attribute('href') or ''
                            if (href and href.startswith('http') and 
                                'google.com' not in href and 
                                'gstatic.com' not in href and
                                'accounts.google.com' not in href):
                                website = href
                                print(f"   ✅ Website (fallback): {website}")
                                break
                        except:
                            continue
                
                # Bước 7: Tìm rating và reviews
                print("⭐ Bước 7: Tìm rating và reviews...")
                all_elements = page.query_selector_all('*')
                rating = None
                reviews_count = None
                
                for elem in all_elements[:100]:  # Chỉ check 100 elements đầu
                    try:
                        text = elem.inner_text().strip()
                        
                        # Tìm rating
                        if not rating and re.match(r'^[1-5]\,[0-9]$', text):
                            rating = float(text.replace(',', '.'))
                            print(f"   ⭐ Rating: {rating}")
                        
                        # Tìm reviews count
                        if not reviews_count and re.search(r'\((\d+)\)', text):
                            match = re.search(r'\((\d+)\)', text)
                            if match:
                                reviews_count = int(match.group(1))
                                print(f"   📊 Reviews: {reviews_count}")
                                
                    except:
                        continue
                
                # Bước 8: Tìm địa chỉ và điện thoại
                print("📍 Bước 8: Tìm địa chỉ và điện thoại...")
                buttons = page.query_selector_all('button')
                divs = page.query_selector_all('div')
                
                address = None
                phone = None
                
                for elem in buttons + divs:
                    try:
                        text = elem.inner_text().strip()
                        
                        # Tìm số điện thoại
                        if (not phone and text and 
                            (text.startswith('+84') or text.startswith('0') or 
                             ('(' in text and ')' in text and any(c.isdigit() for c in text)))):
                            phone = text
                            print(f"   📞 Phone: {phone}")
                        
                        # Tìm địa chỉ
                        if (not address and text and len(text) > 10 and
                            (',' in text or 
                             any(word in text.lower() for word in ['quận', 'huyện', 'phường', 'đường']))):
                            address = text
                            print(f"   📍 Address: {address}")
                            
                    except:
                        continue
                
                # Tóm tắt kết quả
                print(f"\n📋 Tóm tắt kết quả:")
                print(f"   📝 Tên: {title}")
                print(f"   🌐 Website: {website or 'Không có'}")
                print(f"   ⭐ Rating: {rating or 'Không có'}")
                print(f"   📊 Reviews: {reviews_count or 'Không có'}")
                print(f"   📞 Phone: {phone or 'Không có'}")
                print(f"   📍 Address: {address or 'Không có'}")
                
                # Tạo JSON result
                result = {
                    "title": title,
                    "phone": phone or "",
                    "website": website or "",
                    "email": "",
                    "totalScore": rating or 0,
                    "reviewsCount": reviews_count or 0,
                    "street": "",
                    "city": "",
                    "state": "",
                    "countryCode": "VN",
                    "categoryName": "",
                    "url": "",
                    "formatted_address": address or "",
                    "place_id": ""
                }
                
                print(f"\n💾 JSON result:")
                import json
                print(json.dumps(result, ensure_ascii=False, indent=2))
                
        except Exception as e:
            print(f"❌ Lỗi: {e}")
            
        finally:
            input("\nNhấn Enter để đóng browser...")
            browser.close()

if __name__ == "__main__":
    manual_test()
