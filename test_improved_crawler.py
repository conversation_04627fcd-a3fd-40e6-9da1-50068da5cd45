#!/usr/bin/env python3
"""
Test crawler với selector cải tiến từ người dùng
"""

from google_maps_crawler import GoogleMapsCrawler

def main():
    print("🔧 Test Crawler với Selector Cải tiến")
    print("=" * 50)
    
    # Khởi tạo crawler
    crawler = GoogleMapsCrawler(headless=False, timeout=60000)
    
    # Test với từ khóa "Language school"
    query = "Language school"
    print(f"🔍 Tìm kiếm: '{query}' tại Việt Nam")
    print("📍 Sử dụng selector cụ thể để lấy thông tin chi tiết...")
    
    results = crawler.crawl_search_results(
        query=query,
        get_details=True,   # Lấy thông tin chi tiết với selector mới
        max_results=5       # Test với 5 kết quả đầu
    )
    
    print(f"\n✅ Hoàn thành! Tì<PERSON> thấy {len(results)} kết quả")
    
    if results:
        print("\n📋 Chi tiết kết quả:")
        print("-" * 80)
        
        for i, result in enumerate(results, 1):
            print(f"\n{i}. {result.get('title', 'N/A')}")
            print(f"   📍 Địa chỉ: {result.get('formatted_address', 'Chưa có')}")
            print(f"   📞 Điện thoại: {result.get('phone', 'Chưa có')}")
            print(f"   🌐 Website: {result.get('website', 'Chưa có')}")
            print(f"   ⭐ Đánh giá: {result.get('totalScore', 'N/A')} ({result.get('reviewsCount', 0)} reviews)")
            print(f"   🏷️  Loại hình: {result.get('categoryName', 'N/A')}")
            print(f"   🆔 Place ID: {result.get('place_id', 'N/A')}")
        
        # Lưu file
        filename = "improved_crawler_results.json"
        crawler.save_to_json(filename)
        print(f"\n💾 Đã lưu kết quả vào: {filename}")
        
        # Thống kê chi tiết
        print(f"\n📊 Thống kê chi tiết:")
        total = len(results)
        with_address = sum(1 for r in results if r.get('formatted_address'))
        with_phone = sum(1 for r in results if r.get('phone'))
        with_website = sum(1 for r in results if r.get('website'))
        with_rating = sum(1 for r in results if r.get('totalScore', 0) > 0)
        
        print(f"   📍 Có địa chỉ: {with_address}/{total} ({with_address/total*100:.1f}%)")
        print(f"   📞 Có điện thoại: {with_phone}/{total} ({with_phone/total*100:.1f}%)")
        print(f"   🌐 Có website: {with_website}/{total} ({with_website/total*100:.1f}%)")
        print(f"   ⭐ Có đánh giá: {with_rating}/{total} ({with_rating/total*100:.1f}%)")
        
        # Hiển thị kết quả có đầy đủ thông tin nhất
        complete_results = [r for r in results if r.get('formatted_address') and r.get('phone') and r.get('website')]
        if complete_results:
            print(f"\n🏆 Kết quả có thông tin đầy đủ nhất:")
            best = complete_results[0]
            print(f"   📍 {best.get('title')}")
            print(f"   📞 {best.get('phone')}")
            print(f"   🌐 {best.get('website')}")
            print(f"   📍 {best.get('formatted_address')}")
        
    else:
        print("❌ Không tìm thấy kết quả nào")

if __name__ == "__main__":
    main()
