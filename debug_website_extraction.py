#!/usr/bin/env python3
"""
Debug script để kiểm tra việc lấy website từ panel chi tiết
"""

from playwright.sync_api import sync_playwright
import time

def debug_website_extraction():
    """Debug việc lấy website từ panel chi tiết"""
    print("🔍 Debug Website Extraction")
    print("=" * 40)
    
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        context = browser.new_context(
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        )
        page = context.new_page()
        
        try:
            # Truy cập Google Maps
            search_url = "https://www.google.com/maps/search/Language+school/@14.0583,108.2772,6z?gl=vn&hl=vi"
            print(f"📍 Truy cập: {search_url}")
            page.goto(search_url, timeout=60000)
            time.sleep(5)
            
            # Tìm kết quả đầu tiên
            results = page.query_selector_all('a[href*="/place/"]')
            print(f"📊 Tìm thấy {len(results)} kết quả")
            
            if results:
                # Lấy thông tin kết quả đầu tiên
                first_result = results[0]
                result_text = first_result.inner_text().strip()
                result_lines = result_text.split('\n')
                business_name = result_lines[0] if result_lines else "Unknown"
                
                print(f"\n🎯 Kết quả đầu tiên: {business_name}")
                print(f"📝 Full text: {result_text[:100]}...")
                
                # Click vào kết quả
                print(f"\n🖱️  Click vào kết quả...")
                first_result.click()
                
                # Chờ panel chi tiết load
                print("⏳ Chờ panel chi tiết load...")
                time.sleep(8)  # Chờ lâu hơn
                
                # Test selector website do người dùng cung cấp
                print(f"\n🌐 Test selector website:")
                website_selector = "#QA0Szd > div > div > div.w6VYqd > div.bJzME.Hu9e2e.tTVLSc > div > div.e07Vkf.kA9KIf > div > div > div.m6QErb.DxyBCb.kA9KIf.dS8AEf.XiKgde > div:nth-child(7) > div:nth-child(6) > a > div > div.rogA2c.ITvuef > div.Io6YTe.fontBodyMedium.kR99db.fdkmkc"
                
                website_elem = page.query_selector(website_selector)
                if website_elem:
                    website_text = website_elem.inner_text().strip()
                    print(f"✅ Website text: '{website_text}'")
                else:
                    print(f"❌ Không tìm thấy element với selector")
                
                # Test selector link cha
                print(f"\n🔗 Test selector link cha:")
                link_selector = "#QA0Szd > div > div > div.w6VYqd > div.bJzME.Hu9e2e.tTVLSc > div > div.e07Vkf.kA9KIf > div > div > div.m6QErb.DxyBCb.kA9KIf.dS8AEf.XiKgde > div:nth-child(7) > div:nth-child(6) > a"
                
                link_elem = page.query_selector(link_selector)
                if link_elem:
                    href = link_elem.get_attribute('href')
                    print(f"✅ Link href: '{href}'")
                else:
                    print(f"❌ Không tìm thấy link element")
                
                # Tìm tất cả links có text "Trang web"
                print(f"\n🔍 Tìm links có text 'Trang web':")
                all_links = page.query_selector_all('a')
                website_links = []
                
                for i, link in enumerate(all_links[:20]):  # Chỉ check 20 links đầu
                    try:
                        text = link.inner_text().strip()
                        href = link.get_attribute('href') or ''
                        
                        if 'trang web' in text.lower() or 'website' in text.lower():
                            website_links.append((text, href))
                            print(f"  🌐 Found: '{text}' -> '{href}'")
                    except:
                        continue
                
                if not website_links:
                    print("❌ Không tìm thấy link nào có text 'Trang web'")
                
                # Tìm tất cả external links
                print(f"\n🔍 Tìm external links:")
                external_links = []
                
                for link in all_links[:30]:  # Check 30 links đầu
                    try:
                        href = link.get_attribute('href') or ''
                        text = link.inner_text().strip()
                        
                        if (href and href.startswith('http') and 
                            'google.com' not in href and 
                            'gstatic.com' not in href and
                            'accounts.google.com' not in href):
                            
                            external_links.append((text[:50], href))
                            print(f"  🔗 External: '{text[:50]}...' -> '{href}'")
                    except:
                        continue
                
                if not external_links:
                    print("❌ Không tìm thấy external link nào")
                
                # Kiểm tra cấu trúc HTML của panel
                print(f"\n📄 Kiểm tra cấu trúc HTML:")
                
                # Tìm container chính
                main_container = page.query_selector("#QA0Szd > div > div > div.w6VYqd > div.bJzME.Hu9e2e.tTVLSc")
                if main_container:
                    print("✅ Tìm thấy main container")
                    
                    # Tìm tất cả divs con có thể chứa website
                    child_divs = main_container.query_selector_all('div')
                    print(f"📊 Tìm thấy {len(child_divs)} child divs")
                    
                    # Tìm divs có chứa links
                    for i, div in enumerate(child_divs[:10]):
                        try:
                            links_in_div = div.query_selector_all('a')
                            if links_in_div:
                                div_text = div.inner_text().strip()[:50]
                                print(f"  Div {i}: {len(links_in_div)} links - '{div_text}...'")
                        except:
                            continue
                else:
                    print("❌ Không tìm thấy main container")
                
                print(f"\n⏸️  Dừng để bạn có thể inspect thủ công...")
                input("Nhấn Enter để tiếp tục...")
                
        except Exception as e:
            print(f"❌ Lỗi: {e}")
            
        finally:
            browser.close()

if __name__ == "__main__":
    debug_website_extraction()
