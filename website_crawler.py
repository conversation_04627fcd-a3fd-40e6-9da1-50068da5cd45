#!/usr/bin/env python3
"""
Script chuyên biệt để lấy website từ Google Maps
Sử dụng selector cụ thể do người dùng cung cấp
"""

from playwright.sync_api import sync_playwright
import time
import json
import logging

# Thiết lập logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class WebsiteCrawler:
    def __init__(self, headless: bool = False):
        self.headless = headless
        self.results = []
        
    def get_website_from_detail_panel(self, page, business_name: str) -> str:
        """
        Lấy website từ panel chi tiết sử dụng selector cụ thể
        """
        try:
            logger.info(f"Getting website for: {business_name}")
            
            # Chờ panel chi tiết load hoàn toàn
            time.sleep(5)
            
            # Selector website do người dùng cung cấp
            website_selector = "#QA0Szd > div > div > div.w6VYqd > div.bJzME.Hu9e2e.tTVLSc > div > div.e07Vkf.kA9KIf > div > div > div.m6QErb.DxyBCb.kA9KIf.dS8AEf.XiKgde > div:nth-child(7) > div:nth-child(6) > a > div > div.rogA2c.ITvuef > div.Io6YTe.fontBodyMedium.kR99db.fdkmkc"
            
            # Thử selector chính
            website_elem = page.query_selector(website_selector)
            if website_elem:
                website_text = website_elem.inner_text().strip()
                if website_text:
                    # Đảm bảo URL có protocol
                    if not website_text.startswith('http'):
                        website_text = 'https://' + website_text
                    logger.info(f"Found website (text): {website_text}")
                    return website_text
            
            # Thử lấy href từ link cha
            website_link_selector = "#QA0Szd > div > div > div.w6VYqd > div.bJzME.Hu9e2e.tTVLSc > div > div.e07Vkf.kA9KIf > div > div > div.m6QErb.DxyBCb.kA9KIf.dS8AEf.XiKgde > div:nth-child(7) > div:nth-child(6) > a"
            website_link = page.query_selector(website_link_selector)
            if website_link:
                href = website_link.get_attribute('href')
                if href and href.startswith('http'):
                    logger.info(f"Found website (href): {href}")
                    return href
            
            # Thử tìm bằng text "Trang web"
            all_links = page.query_selector_all('a')
            for link in all_links:
                try:
                    text = link.inner_text().strip().lower()
                    if 'trang web' in text or 'website' in text:
                        href = link.get_attribute('href')
                        if href and href.startswith('http') and 'google.com' not in href:
                            logger.info(f"Found website (by text): {href}")
                            return href
                except:
                    continue
            
            # Thử tìm bất kỳ link nào không phải Google
            for link in all_links:
                try:
                    href = link.get_attribute('href') or ''
                    if (href and href.startswith('http') and 
                        'google.com' not in href and 
                        'gstatic.com' not in href and
                        'accounts.google.com' not in href and
                        'support.google.com' not in href):
                        
                        # Kiểm tra xem có phải domain thực không
                        domain = href.replace('http://', '').replace('https://', '').split('/')[0]
                        if '.' in domain and len(domain) > 4:
                            logger.info(f"Found website (fallback): {href}")
                            return href
                except:
                    continue
            
            logger.warning(f"No website found for: {business_name}")
            return ""
            
        except Exception as e:
            logger.error(f"Error getting website for {business_name}: {e}")
            return ""
    
    def crawl_websites(self, query: str = "Language school", max_results: int = 10):
        """
        Crawl websites cho các trung tâm ngoại ngữ
        """
        logger.info(f"Starting website crawl for: {query}")
        
        with sync_playwright() as p:
            # Khởi tạo browser
            browser = p.chromium.launch(
                headless=self.headless,
                args=[
                    '--no-sandbox',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-dev-shm-usage'
                ]
            )
            
            context = browser.new_context(
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                viewport={'width': 1920, 'height': 1080}
            )
            
            page = context.new_page()
            
            try:
                # Truy cập Google Maps
                search_url = f"https://www.google.com/maps/search/{query.replace(' ', '+')}/@14.0583,108.2772,6z?gl=vn&hl=vi"
                logger.info(f"Navigating to: {search_url}")
                page.goto(search_url, wait_until='domcontentloaded', timeout=60000)
                
                # Chờ kết quả load
                time.sleep(5)
                page.wait_for_selector('div[role="feed"]', timeout=30000)
                
                # Tìm tất cả kết quả
                result_links = page.query_selector_all('a[href*="/place/"]')
                logger.info(f"Found {len(result_links)} results")
                
                if len(result_links) > max_results:
                    result_links = result_links[:max_results]
                
                results = []
                
                for i, link in enumerate(result_links):
                    try:
                        # Lấy tên business
                        business_name = link.inner_text().strip().split('\n')[0]
                        if not business_name or len(business_name) < 3:
                            continue
                            
                        logger.info(f"Processing {i+1}/{len(result_links)}: {business_name}")
                        
                        # Click vào kết quả
                        link.click()
                        
                        # Lấy website từ panel chi tiết
                        website = self.get_website_from_detail_panel(page, business_name)
                        
                        # Lưu kết quả
                        result = {
                            "title": business_name,
                            "website": website,
                            "index": i + 1
                        }
                        results.append(result)
                        
                        # Log kết quả
                        if website:
                            logger.info(f"✅ {business_name}: {website}")
                        else:
                            logger.info(f"❌ {business_name}: No website found")
                        
                        # Chờ trước khi chuyển sang kết quả tiếp theo
                        time.sleep(2)
                        
                    except Exception as e:
                        logger.error(f"Error processing result {i+1}: {e}")
                        continue
                
                self.results = results
                logger.info(f"Completed crawling. Found websites for {sum(1 for r in results if r['website'])} out of {len(results)} businesses")
                
            except Exception as e:
                logger.error(f"Error during crawling: {e}")
                
            finally:
                browser.close()
                
        return self.results
    
    def save_results(self, filename: str = "websites_results.json"):
        """
        Lưu kết quả ra file JSON
        """
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)
        
        logger.info(f"Results saved to: {filename}")
        return filename
    
    def print_summary(self):
        """
        In tóm tắt kết quả
        """
        if not self.results:
            print("No results found.")
            return
            
        print(f"\n=== WEBSITE CRAWL SUMMARY ===")
        print(f"Total businesses: {len(self.results)}")
        
        with_website = [r for r in self.results if r.get('website')]
        print(f"Found websites: {len(with_website)}")
        print(f"Success rate: {len(with_website)/len(self.results)*100:.1f}%")
        
        print(f"\n=== RESULTS ===")
        for result in self.results:
            status = "✅" if result.get('website') else "❌"
            print(f"{status} {result.get('title', 'N/A')}")
            if result.get('website'):
                print(f"    🌐 {result['website']}")
            print()

def main():
    """
    Hàm main để test website crawler
    """
    print("🌐 Website Crawler cho Google Maps")
    print("=" * 50)
    
    # Khởi tạo crawler (hiển thị browser để debug)
    crawler = WebsiteCrawler(headless=False)
    
    # Crawl websites
    results = crawler.crawl_websites(
        query="Language school",
        max_results=5  # Test với 5 kết quả đầu
    )
    
    # In tóm tắt
    crawler.print_summary()
    
    # Lưu file
    filename = crawler.save_results("language_school_websites.json")
    print(f"\n💾 Results saved to: {filename}")

if __name__ == "__main__":
    main()
