#!/usr/bin/env python3
"""
Debug script để kiểm tra cấu trúc HTML của panel chi tiết
"""

from playwright.sync_api import sync_playwright
import time

def debug_detail_panel():
    """Debug panel chi tiết khi click vào một kết quả"""
    print("🔍 Debug Panel Chi tiết Google Maps...")
    
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        context = browser.new_context(
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        )
        page = context.new_page()
        
        try:
            # Truy cập Google Maps với tìm kiếm
            search_url = "https://www.google.com/maps/search/Language+school/@14.0583,108.2772,6z?gl=vn&hl=vi"
            print(f"📍 Truy cập: {search_url}")
            
            page.goto(search_url, timeout=60000)
            time.sleep(5)
            
            # Tìm kết quả đầu tiên
            results = page.query_selector_all('a[href*="/place/"]')
            print(f"📊 Tìm thấy {len(results)} kết quả")
            
            if results:
                first_result = results[0]
                result_title = first_result.inner_text().strip()
                print(f"\n🎯 Click vào kết quả đầu tiên: {result_title}")
                
                # Click vào kết quả
                first_result.click()
                print("⏳ Chờ panel chi tiết load...")
                time.sleep(8)  # Chờ lâu hơn để panel load đầy đủ
                
                print("\n🔍 Kiểm tra các selector do người dùng cung cấp:")
                print("-" * 60)
                
                # Test các selector cụ thể
                selectors_to_test = [
                    ("Tên địa điểm", "h1.DUwDvf.lfPIob"),
                    ("Rating", "#QA0Szd > div > div > div.w6VYqd > div.bJzME.Hu9e2e.tTVLSc > div > div.e07Vkf.kA9KIf > div > div > div.m6QErb.DxyBCb.kA9KIf.dS8AEf.XiKgde > div.TIHn2 > div > div.lMbq3e > div.LBgpqf > div > div.fontBodyMedium.dmRWX > div.F7nice > span:nth-child(1) > span:nth-child(1)"),
                    ("Reviews Count", "#QA0Szd > div > div > div.w6VYqd > div.bJzME.Hu9e2e.tTVLSc > div > div.e07Vkf.kA9KIf > div > div > div.m6QErb.DxyBCb.kA9KIf.dS8AEf.XiKgde > div.TIHn2 > div > div.lMbq3e > div.LBgpqf > div > div.fontBodyMedium.dmRWX > div.F7nice > span:nth-child(2) > span > span"),
                    ("Địa chỉ", "#QA0Szd > div > div > div.w6VYqd > div.bJzME.Hu9e2e.tTVLSc > div > div.e07Vkf.kA9KIf > div > div > div.m6QErb.DxyBCb.kA9KIf.dS8AEf.XiKgde > div:nth-child(7) > div:nth-child(3) > button > div > div.rogA2c > div.Io6YTe.fontBodyMedium.kR99db.fdkmkc"),
                    ("Website", "#QA0Szd > div > div > div.w6VYqd > div.bJzME.Hu9e2e.tTVLSc > div > div.e07Vkf.kA9KIf > div > div > div.m6QErb.DxyBCb.kA9KIf.dS8AEf.XiKgde > div:nth-child(7) > div:nth-child(6) > a > div > div.rogA2c.ITvuef > div.Io6YTe.fontBodyMedium.kR99db.fdkmkc"),
                    ("Điện thoại", "#QA0Szd > div > div > div.w6VYqd > div.bJzME.Hu9e2e.tTVLSc > div > div.e07Vkf.kA9KIf > div > div > div.m6QErb.DxyBCb.kA9KIf.dS8AEf.XiKgde > div:nth-child(7) > div:nth-child(7) > button > div > div.rogA2c > div.Io6YTe.fontBodyMedium.kR99db.fdkmkc"),
                ]
                
                for name, selector in selectors_to_test:
                    try:
                        elem = page.query_selector(selector)
                        if elem:
                            text = elem.inner_text().strip()
                            print(f"  ✅ {name}: '{text}'")
                        else:
                            print(f"  ❌ {name}: Không tìm thấy")
                    except Exception as e:
                        print(f"  ⚠️  {name}: Lỗi - {e}")
                
                # Thử tìm các selector thay thế
                print(f"\n🔍 Tìm selector thay thế:")
                print("-" * 60)
                
                # Tìm tất cả h1 elements
                h1_elements = page.query_selector_all('h1')
                print(f"📝 Tìm thấy {len(h1_elements)} h1 elements:")
                for i, h1 in enumerate(h1_elements[:3]):
                    try:
                        text = h1.inner_text().strip()
                        classes = h1.get_attribute('class') or ''
                        print(f"  {i+1}. <h1 class='{classes}'> {text}")
                    except:
                        pass
                
                # Tìm tất cả elements có text chứa số điện thoại
                print(f"\n📞 Tìm elements có thể chứa số điện thoại:")
                all_elements = page.query_selector_all('*')
                phone_patterns = ['+84', '0', '(', ')']
                for elem in all_elements[:50]:  # Chỉ check 50 elements đầu
                    try:
                        text = elem.inner_text().strip()
                        if text and any(pattern in text for pattern in phone_patterns) and len(text) < 50:
                            tag = elem.tag_name
                            classes = elem.get_attribute('class') or ''
                            print(f"  📞 <{tag} class='{classes[:30]}...'> {text}")
                    except:
                        pass
                
                # Tìm tất cả links có thể là website
                print(f"\n🌐 Tìm links có thể là website:")
                links = page.query_selector_all('a')
                for link in links[:20]:  # Chỉ check 20 links đầu
                    try:
                        href = link.get_attribute('href') or ''
                        text = link.inner_text().strip()
                        if href and ('http' in href or '.com' in href or '.vn' in href):
                            print(f"  🌐 href='{href[:50]}...' text='{text[:30]}...'")
                    except:
                        pass
                
                print(f"\n⏸️  Dừng để bạn có thể inspect thủ công...")
                print(f"💡 Hãy mở DevTools (F12) và tìm selector chính xác")
                input("Nhấn Enter để tiếp tục...")
                
        except Exception as e:
            print(f"❌ Lỗi: {e}")
            
        finally:
            browser.close()

if __name__ == "__main__":
    debug_detail_panel()
