#!/usr/bin/env python3
"""
Test parsing text từ Google Maps
"""

from playwright.sync_api import sync_playwright
import time
import re

def test_text_parsing():
    """Test parsing text từ danh sách"""
    print("🔧 Test Text Parsing")
    print("=" * 40)
    
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        context = browser.new_context(
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        )
        page = context.new_page()
        
        try:
            # Truy cập Google Maps
            search_url = "https://www.google.com/maps/search/Language+school/@14.0583,108.2772,6z?gl=vn&hl=vi"
            print(f"📍 Truy cập: {search_url}")
            page.goto(search_url, timeout=60000)
            time.sleep(5)
            
            # Lấy text từ feed container
            feed_container = page.query_selector('div[role="feed"]')
            if feed_container:
                full_text = feed_container.inner_text()
                lines = full_text.split('\n')
                
                print(f"📊 Tổng số dòng: {len(lines)}")
                print(f"📝 Một số dòng đầu:")
                
                businesses = []
                current_business = None
                
                for i, line in enumerate(lines[:50]):  # Chỉ xem 50 dòng đầu
                    line = line.strip()
                    if not line:
                        continue
                        
                    print(f"  {i:2d}: '{line}'")
                    
                    # Phát hiện tên business
                    if (line and 
                        not re.search(r'[\d,\(\)]', line) and 
                        not any(word in line.lower() for word in ['đang mở', 'đóng cửa', 'đường đi', 'trang web', 'giờ', 'phút', 'kết quả', 'chia sẻ']) and
                        len(line) > 5 and len(line) < 100 and
                        line not in ['Kết quả', 'Chia sẻ', 'Đường đi', 'Trang web']):
                        
                        if current_business:
                            businesses.append(current_business)
                        
                        current_business = {
                            'title': line,
                            'rating': None,
                            'reviews': None,
                            'phone': None,
                            'address': None,
                            'category': None
                        }
                        print(f"    🏢 NEW BUSINESS: {line}")
                    
                    # Phát hiện rating và reviews
                    elif re.match(r'^[1-5]\,[0-9]\(\d+\)$', line):
                        if current_business:
                            parts = line.split('(')
                            rating = float(parts[0].replace(',', '.'))
                            reviews = int(parts[1].replace(')', ''))
                            current_business['rating'] = rating
                            current_business['reviews'] = reviews
                            print(f"    ⭐ RATING: {rating} ({reviews} reviews)")
                    
                    # Phát hiện số điện thoại
                    elif re.match(r'^0\d{2,3}\s?\d{3,4}\s?\d{3,4}$', line):
                        if current_business:
                            current_business['phone'] = line
                            print(f"    📞 PHONE: {line}")
                    
                    # Phát hiện địa chỉ
                    elif (',' in line or 
                          any(word in line.lower() for word in ['quận', 'huyện', 'phường', 'đường', 'p.', 'q.']) and
                          not any(word in line.lower() for word in ['đang mở', 'đóng cửa', 'trang web', 'đường đi'])):
                        if current_business and not current_business.get('address'):
                            current_business['address'] = line
                            print(f"    📍 ADDRESS: {line}")
                    
                    # Phát hiện category
                    elif (current_business and not current_business.get('category') and
                          not re.search(r'[\d\(\)]', line) and
                          any(word in line.lower() for word in ['trường', 'trung tâm', 'viện', 'học', 'giáo dục', 'ngoại ngữ'])):
                        current_business['category'] = line
                        print(f"    🏷️  CATEGORY: {line}")
                
                # Lưu business cuối cùng
                if current_business:
                    businesses.append(current_business)
                
                print(f"\n📋 Tóm tắt kết quả:")
                print(f"   Tổng số businesses: {len(businesses)}")
                
                for i, business in enumerate(businesses[:5]):
                    print(f"\n{i+1}. {business.get('title', 'N/A')}")
                    print(f"   ⭐ {business.get('rating', 'N/A')} ({business.get('reviews', 0)} reviews)")
                    print(f"   📞 {business.get('phone', 'N/A')}")
                    print(f"   📍 {business.get('address', 'N/A')}")
                    print(f"   🏷️  {business.get('category', 'N/A')}")
                
                # Tạo JSON
                import json
                with open('parsed_results.json', 'w', encoding='utf-8') as f:
                    json.dump(businesses, f, ensure_ascii=False, indent=2)
                print(f"\n💾 Đã lưu vào: parsed_results.json")
                
        except Exception as e:
            print(f"❌ Lỗi: {e}")
            
        finally:
            input("\nNhấn Enter để đóng browser...")
            browser.close()

if __name__ == "__main__":
    test_text_parsing()
