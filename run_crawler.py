#!/usr/bin/env python3
"""
Script đơn giản để chạy Google Maps Crawler
"""

from google_maps_crawler import GoogleMapsCrawler
import argparse
import sys

def main():
    parser = argparse.ArgumentParser(description='Google Maps Crawler')
    parser.add_argument('query', help='Từ khóa tìm kiếm (ví dụ: "Language school")')
    parser.add_argument('--max-results', type=int, default=50, help='Số kết quả tối đa (mặc định: 50)')
    parser.add_argument('--headless', action='store_true', help='Chạy browser ẩn')
    parser.add_argument('--get-details', action='store_true', help='Lấy thông tin chi tiết (phone, website)')
    parser.add_argument('--output', help='Tên file output (mặc định: tự động)')
    
    args = parser.parse_args()
    
    print(f"🔍 Tìm kiếm: '{args.query}' trên Google Maps (Việt Nam)")
    print(f"📊 Giới hạn: {args.max_results} kết quả")
    print(f"🔧 Chi tiết: {'Có' if args.get_details else 'Không'}")
    print(f"👁️  Hiển thị: {'Ẩn' if args.headless else 'Hiện'}")
    print("-" * 50)
    
    # Khởi tạo crawler
    crawler = GoogleMapsCrawler(headless=args.headless)
    
    try:
        # Bắt đầu crawl
        results = crawler.crawl_search_results(
            query=args.query,
            get_details=args.get_details,
            max_results=args.max_results
        )
        
        if results:
            # In tóm tắt
            crawler.print_summary()
            
            # Lưu file
            filename = crawler.save_to_json(args.output)
            print(f"\n✅ Hoàn thành! Kết quả đã lưu vào: {filename}")
            
        else:
            print("❌ Không tìm thấy kết quả nào!")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⏹️  Đã dừng bởi người dùng")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
