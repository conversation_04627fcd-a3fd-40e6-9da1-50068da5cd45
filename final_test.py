#!/usr/bin/env python3
"""
Test cuối cùng với từ khóa "Language school" như yêu cầu
"""

from google_maps_crawler import GoogleMapsCrawler

def main():
    print("🎯 Test cuối cùng - Language School tại Việt Nam")
    print("=" * 50)
    
    # Khởi tạo crawler
    crawler = GoogleMapsCrawler(headless=False, timeout=60000)
    
    # Test với từ khóa yêu cầu
    query = "Language school"
    print(f"🔍 Tìm kiếm: '{query}' tại Việt Nam")
    
    results = crawler.crawl_search_results(
        query=query,
        get_details=True,   # Lấy thông tin chi tiết
        max_results=10      # Lấy 10 kết quả đầu
    )
    
    print(f"\n📊 Tổng cộng: {len(results)} trung tâm ngoại ngữ")
    
    if results:
        print("\n📋 Danh sách trung tâm ngoại ngữ:")
        print("-" * 80)
        
        for i, result in enumerate(results, 1):
            print(f"\n{i}. {result.get('title', 'N/A')}")
            print(f"   📍 Địa chỉ: {result.get('formatted_address', 'N/A')}")
            print(f"   📞 Điện thoại: {result.get('phone', 'N/A')}")
            print(f"   🌐 Website: {result.get('website', 'N/A')}")
            print(f"   ⭐ Đánh giá: {result.get('totalScore', 'N/A')} ({result.get('reviewsCount', 0)} reviews)")
            print(f"   🏷️  Loại hình: {result.get('categoryName', 'N/A')}")
            print(f"   🔗 URL: {result.get('url', 'N/A')[:80]}...")
        
        # Lưu file với tên cụ thể
        filename = "language_schools_vietnam.json"
        crawler.save_to_json(filename)
        print(f"\n💾 Đã lưu kết quả vào: {filename}")
        
        # Thống kê
        print(f"\n📈 Thống kê:")
        print(f"   - Có số điện thoại: {sum(1 for r in results if r.get('phone'))}")
        print(f"   - Có website: {sum(1 for r in results if r.get('website'))}")
        print(f"   - Có địa chỉ: {sum(1 for r in results if r.get('formatted_address'))}")
        print(f"   - Có đánh giá: {sum(1 for r in results if r.get('totalScore', 0) > 0)}")
        
        # Tìm trung tâm có rating cao nhất
        rated_results = [r for r in results if r.get('totalScore', 0) > 0]
        if rated_results:
            best_rated = max(rated_results, key=lambda x: x.get('totalScore', 0))
            print(f"\n🏆 Trung tâm có đánh giá cao nhất:")
            print(f"   {best_rated.get('title')} - {best_rated.get('totalScore')}⭐ ({best_rated.get('reviewsCount')} reviews)")
        
    else:
        print("❌ Không tìm thấy kết quả nào")

if __name__ == "__main__":
    main()
