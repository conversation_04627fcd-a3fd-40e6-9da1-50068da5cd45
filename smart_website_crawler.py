#!/usr/bin/env python3
"""
Smart Website Crawler - Lấy website chính xác cho từng business
"""

from playwright.sync_api import sync_playwright
import time
import json
import logging
import re

# Thiết lập logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SmartWebsiteCrawler:
    def __init__(self, headless: bool = False):
        self.headless = headless
        self.results = []
        
    def get_business_list_with_websites(self, page):
        """
        Lấy danh sách businesses và websites từ danh sách bên trái
        """
        businesses = []
        
        try:
            # Lấy text từ feed container
            feed_container = page.query_selector('div[role="feed"]')
            if not feed_container:
                return businesses
                
            full_text = feed_container.inner_text()
            lines = full_text.split('\n')
            
            current_business = None
            i = 0
            
            while i < len(lines):
                line = lines[i].strip()
                
                if not line:
                    i += 1
                    continue
                
                # Ph<PERSON>t hiện tên business
                if (line and 
                    not re.search(r'[\d,\(\)]', line) and 
                    not any(word in line.lower() for word in ['đang mở', 'đóng cửa', 'đường đi', 'trang web', 'giờ', 'phút', 'kết quả', 'chia sẻ', 'chưa có bài đánh giá', 'dịch vụ tại chỗ', 'lớp học trực tuyến']) and
                    len(line) > 5 and len(line) < 100 and
                    line not in ['Kết quả', 'Chia sẻ', 'Đường đi', 'Trang web', 'Chưa có bài đánh giá']):
                    
                    # Lưu business trước đó
                    if current_business:
                        businesses.append(current_business.copy())
                    
                    # Bắt đầu business mới
                    current_business = {
                        "title": line,
                        "phone": "",
                        "website": "",
                        "rating": 0,
                        "reviews": 0,
                        "address": "",
                        "order": len(businesses) + 1
                    }
                
                # Phát hiện rating và reviews
                elif current_business and re.match(r'^[1-5]\,[0-9]\(\d+\)$', line):
                    parts = line.split('(')
                    rating = float(parts[0].replace(',', '.'))
                    reviews = int(parts[1].replace(')', ''))
                    current_business['rating'] = rating
                    current_business['reviews'] = reviews
                
                # Phát hiện số điện thoại
                elif current_business and ('đang mở' in line.lower() or 'đóng cửa' in line.lower()):
                    phone_match = re.search(r'0\d{2,3}\s?\d{3,4}\s?\d{3,4}', line)
                    if phone_match:
                        current_business['phone'] = phone_match.group(0)
                
                # Phát hiện địa chỉ
                elif (current_business and not current_business.get('address') and
                      ('p.' in line.lower() or 'ng.' in line.lower() or 'no ' in line.lower()) and
                      any(word in line.lower() for word in ['trường', 'trung tâm', 'viện', 'p.', 'ng.', 'no'])):
                    if '·' in line:
                        address_part = line.split('·')[-1].strip()
                        current_business['address'] = address_part
                    else:
                        current_business['address'] = line
                
                i += 1
            
            # Lưu business cuối cùng
            if current_business:
                businesses.append(current_business)
                
            logger.info(f"Found {len(businesses)} businesses in list")
            return businesses
            
        except Exception as e:
            logger.error(f"Error parsing business list: {e}")
            return businesses
    
    def click_and_get_website(self, page, business_name: str, business_order: int):
        """
        Click vào business cụ thể và lấy website của nó
        """
        try:
            # Tìm link của business này
            result_links = page.query_selector_all('a[href*="/place/"]')
            
            if business_order <= len(result_links):
                target_link = result_links[business_order - 1]
                
                logger.info(f"Clicking on business #{business_order}: {business_name}")
                target_link.click()
                time.sleep(4)  # Chờ panel load
                
                # Lấy tất cả website links
                all_links = page.query_selector_all('a')
                websites = []
                
                for link in all_links:
                    try:
                        text = link.inner_text().strip()
                        href = link.get_attribute('href') or ''
                        
                        if ('trang web' in text.lower() and 
                            href and href.startswith('http') and 
                            'google.com' not in href):
                            websites.append(href)
                    except:
                        continue
                
                # Nếu có nhiều websites, chọn cái đầu tiên (thường là của business hiện tại)
                if websites:
                    website = websites[0]
                    logger.info(f"Found website for {business_name}: {website}")
                    return website
                else:
                    logger.info(f"No website found for {business_name}")
                    return ""
                    
        except Exception as e:
            logger.error(f"Error getting website for {business_name}: {e}")
            return ""
    
    def crawl_with_websites(self, query: str = "Language school", max_results: int = 10):
        """
        Crawl businesses với websites chính xác
        """
        logger.info(f"Starting smart website crawl for: {query}")
        
        with sync_playwright() as p:
            browser = p.chromium.launch(
                headless=self.headless,
                args=['--no-sandbox', '--disable-blink-features=AutomationControlled']
            )
            
            context = browser.new_context(
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            )
            
            page = context.new_page()
            
            try:
                # Truy cập Google Maps
                search_url = f"https://www.google.com/maps/search/{query.replace(' ', '+')}/@14.0583,108.2772,6z?gl=vn&hl=vi"
                logger.info(f"Navigating to: {search_url}")
                page.goto(search_url, timeout=60000)
                
                # Chờ và scroll để load kết quả
                time.sleep(5)
                page.wait_for_selector('div[role="feed"]', timeout=30000)
                
                # Scroll để load thêm kết quả
                for i in range(5):
                    page.evaluate("document.querySelector('div[role=\"feed\"]').scrollBy(0, 1000)")
                    time.sleep(1)
                
                # Lấy danh sách businesses từ text
                businesses = self.get_business_list_with_websites(page)
                
                if len(businesses) > max_results:
                    businesses = businesses[:max_results]
                
                logger.info(f"Processing {len(businesses)} businesses")
                
                # Lấy website cho từng business
                for business in businesses:
                    website = self.click_and_get_website(page, business['title'], business['order'])
                    business['website'] = website
                    
                    # Chờ giữa các lần click
                    time.sleep(2)
                
                self.results = businesses
                logger.info(f"Completed crawling with {sum(1 for b in businesses if b['website'])} websites found")
                
            except Exception as e:
                logger.error(f"Error during crawling: {e}")
                
            finally:
                browser.close()
                
        return self.results
    
    def save_results(self, filename: str = "smart_crawl_results.json"):
        """Lưu kết quả"""
        # Format theo yêu cầu
        formatted_results = []
        for business in self.results:
            # Parse địa chỉ
            address = business.get('address', '')
            street, city, state = "", "", ""
            if address:
                parts = address.split(',')
                street = parts[0].strip() if len(parts) > 0 else ""
                city = parts[1].strip() if len(parts) > 1 else ""
                state = parts[2].strip() if len(parts) > 2 else ""
            
            formatted_result = {
                "title": business.get('title', ''),
                "phone": business.get('phone', ''),
                "website": business.get('website', ''),
                "email": "",
                "totalScore": business.get('rating', 0),
                "reviewsCount": business.get('reviews', 0),
                "street": street,
                "city": city,
                "state": state,
                "countryCode": "VN",
                "categoryName": "",
                "url": "",
                "formatted_address": address,
                "place_id": business.get('title', '').replace(' ', '+')
            }
            formatted_results.append(formatted_result)
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(formatted_results, f, ensure_ascii=False, indent=2)
        
        logger.info(f"Results saved to: {filename}")
        return filename
    
    def print_summary(self):
        """In tóm tắt"""
        if not self.results:
            print("No results found.")
            return
            
        print(f"\n=== SMART WEBSITE CRAWL SUMMARY ===")
        print(f"Total businesses: {len(self.results)}")
        
        with_website = [r for r in self.results if r.get('website')]
        with_phone = [r for r in self.results if r.get('phone')]
        with_rating = [r for r in self.results if r.get('rating', 0) > 0]
        
        print(f"Found websites: {len(with_website)}")
        print(f"Found phones: {len(with_phone)}")
        print(f"Found ratings: {len(with_rating)}")
        print(f"Website success rate: {len(with_website)/len(self.results)*100:.1f}%")
        
        print(f"\n=== DETAILED RESULTS ===")
        for i, business in enumerate(self.results, 1):
            print(f"\n{i}. {business.get('title', 'N/A')}")
            if business.get('rating', 0) > 0:
                print(f"   ⭐ {business['rating']} ({business['reviews']} reviews)")
            if business.get('phone'):
                print(f"   📞 {business['phone']}")
            if business.get('website'):
                print(f"   🌐 {business['website']}")
            if business.get('address'):
                print(f"   📍 {business['address']}")

def main():
    """Test smart crawler"""
    print("🧠 Smart Website Crawler cho Google Maps")
    print("=" * 50)
    
    crawler = SmartWebsiteCrawler(headless=False)
    
    results = crawler.crawl_with_websites(
        query="Language school",
        max_results=8  # Test với 8 kết quả
    )
    
    crawler.print_summary()
    
    filename = crawler.save_results("language_schools_with_websites.json")
    print(f"\n💾 Results saved to: {filename}")

if __name__ == "__main__":
    main()
