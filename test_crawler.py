#!/usr/bin/env python3
"""
Script test để kiểm tra Google Maps Crawler
"""

from google_maps_crawler import GoogleMapsCrawler
import json

def test_basic_search():
    """Test tìm kiếm cơ bản"""
    print("🧪 Test 1: Tì<PERSON> kiếm cơ bản")
    print("-" * 30)
    
    crawler = GoogleMapsCrawler(headless=False)  # Hiển thị browser để xem
    
    # Test với từ khóa đơn giản
    results = crawler.crawl_search_results(
        query="Coffee shop",
        get_details=False,
        max_results=5  # Chỉ lấy 5 kết quả để test nhanh
    )
    
    print(f"✅ Tìm thấy {len(results)} kết quả")
    
    # Kiểm tra cấu trúc dữ liệu
    if results:
        first_result = results[0]
        required_fields = ['title', 'formatted_address', 'place_id']
        
        for field in required_fields:
            if field in first_result and first_result[field]:
                print(f"✅ {field}: {first_result[field][:50]}...")
            else:
                print(f"❌ Thiếu {field}")
    
    return results

def test_detailed_search():
    """Test tìm kiếm với thông tin chi tiết"""
    print("\n🧪 Test 2: Tìm kiếm chi tiết")
    print("-" * 30)
    
    crawler = GoogleMapsCrawler(headless=False)
    
    # Test với lấy thông tin chi tiết
    results = crawler.crawl_search_results(
        query="Language school",
        get_details=True,
        max_results=3  # Ít kết quả vì lấy chi tiết lâu hơn
    )
    
    print(f"✅ Tìm thấy {len(results)} kết quả với chi tiết")
    
    # Kiểm tra thông tin chi tiết
    if results:
        for i, result in enumerate(results):
            print(f"\nKết quả {i+1}:")
            print(f"  Tên: {result.get('title', 'N/A')}")
            print(f"  Điện thoại: {result.get('phone', 'N/A')}")
            print(f"  Website: {result.get('website', 'N/A')}")
            print(f"  Rating: {result.get('totalScore', 'N/A')}")
    
    return results

def test_json_output():
    """Test xuất JSON"""
    print("\n🧪 Test 3: Xuất JSON")
    print("-" * 30)
    
    crawler = GoogleMapsCrawler(headless=True)  # Chạy nhanh
    
    results = crawler.crawl_search_results(
        query="Restaurant",
        get_details=False,
        max_results=3
    )
    
    # Lưu file test
    filename = "test_results.json"
    saved_file = crawler.save_to_json(filename)
    
    # Kiểm tra file đã lưu
    try:
        with open(saved_file, 'r', encoding='utf-8') as f:
            loaded_data = json.load(f)
        
        print(f"✅ File JSON đã lưu: {saved_file}")
        print(f"✅ Số kết quả trong file: {len(loaded_data)}")
        
        # Kiểm tra cấu trúc JSON
        if loaded_data and isinstance(loaded_data, list):
            sample = loaded_data[0]
            print(f"✅ Cấu trúc JSON hợp lệ")
            print(f"   Sample title: {sample.get('title', 'N/A')}")
        
    except Exception as e:
        print(f"❌ Lỗi đọc file JSON: {e}")
    
    return results

def main():
    """Chạy tất cả tests"""
    print("🚀 Bắt đầu test Google Maps Crawler")
    print("=" * 50)
    
    try:
        # Test 1: Tìm kiếm cơ bản
        results1 = test_basic_search()
        
        # Test 2: Tìm kiếm chi tiết (chỉ chạy nếu test 1 thành công)
        if results1:
            results2 = test_detailed_search()
        
        # Test 3: Xuất JSON
        results3 = test_json_output()
        
        print("\n🎉 Tất cả tests hoàn thành!")
        print("\n📊 Tóm tắt:")
        print(f"  Test 1 (Basic): {len(results1) if results1 else 0} kết quả")
        print(f"  Test 2 (Detail): {len(results2) if 'results2' in locals() and results2 else 0} kết quả")
        print(f"  Test 3 (JSON): {len(results3) if results3 else 0} kết quả")
        
    except KeyboardInterrupt:
        print("\n⏹️  Test bị dừng bởi người dùng")
    except Exception as e:
        print(f"\n❌ Lỗi trong quá trình test: {e}")

if __name__ == "__main__":
    main()
