#!/usr/bin/env python3
"""
Demo script cho Google Maps Crawler
Thực hiện đúng yêu cầu của người dùng: crawl "Language school" tại Việt Nam
"""

from google_maps_crawler import GoogleMapsCrawler
import json

def main():
    print("🎯 DEMO: Google Maps Crawler")
    print("Crawl thông tin trung tâm ngoại ngữ tại Việt Nam")
    print("=" * 60)
    
    # Khởi tạo crawler
    print("🔧 Khởi tạo crawler...")
    crawler = GoogleMapsCrawler(
        headless=False,  # Hiển thị browser để demo
        timeout=60000    # 60 giây timeout
    )
    
    # Thực hiện crawl theo yêu cầu
    query = "Language school"
    print(f"🔍 Tìm kiếm: '{query}' tại Việt Nam")
    print("⏳ Đang crawl dữ liệu...")
    
    results = crawler.crawl_search_results(
        query=query,
        get_details=True,   # L<PERSON>y thông tin chi tiết
        max_results=20      # Lấy 20 kết quả đầu
    )
    
    print(f"\n✅ Hoàn thành! Tìm thấy {len(results)} trung tâm ngoại ngữ")
    
    if results:
        # Hiển thị kết quả mẫu
        print("\n📋 Một số kết quả mẫu:")
        print("-" * 60)
        
        for i, result in enumerate(results[:5], 1):  # Hiển thị 5 đầu
            print(f"\n{i}. {result.get('title', 'N/A')}")
            if result.get('totalScore', 0) > 0:
                print(f"   ⭐ {result.get('totalScore')} ({result.get('reviewsCount')} reviews)")
            if result.get('formatted_address'):
                print(f"   📍 {result.get('formatted_address')}")
            if result.get('phone'):
                print(f"   📞 {result.get('phone')}")
            if result.get('website'):
                print(f"   🌐 {result.get('website')}")
        
        # Lưu file JSON theo format yêu cầu
        output_file = "language_schools_vietnam_demo.json"
        
        # Format lại dữ liệu theo yêu cầu
        formatted_results = []
        for result in results:
            formatted_result = {
                "title": result.get('title', ''),
                "phone": result.get('phone', ''),
                "website": result.get('website', ''),
                "email": result.get('email', ''),
                "totalScore": result.get('totalScore', 0),
                "reviewsCount": result.get('reviewsCount', 0),
                "street": result.get('street', ''),
                "city": result.get('city', ''),
                "state": result.get('state', ''),
                "countryCode": "VN",
                "categoryName": result.get('categoryName', ''),
                "url": result.get('url', ''),
                "formatted_address": result.get('formatted_address', ''),
                "place_id": result.get('place_id', '')
            }
            formatted_results.append(formatted_result)
        
        # Lưu file
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(formatted_results, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 Đã lưu {len(results)} kết quả vào: {output_file}")
        
        # Thống kê
        print(f"\n📊 Thống kê:")
        print(f"   📍 Tổng số trung tâm: {len(results)}")
        print(f"   ⭐ Có đánh giá: {sum(1 for r in results if r.get('totalScore', 0) > 0)}")
        print(f"   📞 Có số điện thoại: {sum(1 for r in results if r.get('phone'))}")
        print(f"   🌐 Có website: {sum(1 for r in results if r.get('website'))}")
        print(f"   📍 Có địa chỉ: {sum(1 for r in results if r.get('formatted_address'))}")
        
        # Top rated
        rated_results = [r for r in results if r.get('totalScore', 0) > 0]
        if rated_results:
            top_rated = sorted(rated_results, key=lambda x: x.get('totalScore', 0), reverse=True)[:3]
            print(f"\n🏆 Top 3 trung tâm có đánh giá cao nhất:")
            for i, result in enumerate(top_rated, 1):
                print(f"   {i}. {result.get('title')} - {result.get('totalScore')}⭐ ({result.get('reviewsCount')} reviews)")
        
        print(f"\n🎉 Demo hoàn thành!")
        print(f"📄 File kết quả: {output_file}")
        print(f"🔍 Định dạng JSON đúng như yêu cầu với các trường:")
        print(f"   title, phone, website, email, totalScore, reviewsCount,")
        print(f"   street, city, state, countryCode, categoryName, url,")
        print(f"   formatted_address, place_id")
        
    else:
        print("❌ Không tìm thấy kết quả nào")

if __name__ == "__main__":
    main()
