# Google Maps Crawler

Công cụ crawl dữ liệu từ Google Maps hoàn toàn miễn phí, không sử dụng API.

## ✨ Tính năng

- 🔍 Tìm kiếm theo từ khóa với focus vào Việt Nam
- 📊 Crawl tất cả kết quả (infinite scroll)
- 📱 L<PERSON>y thông tin chi tiết: tên, địa chỉ, đi<PERSON>n thoại, website, rating
- 💾 Xuất kết quả ra JSON
- 🤖 Tự động hóa hoàn toàn với Playwright
- 🚫 Không cần API key hay tính phí

## 🚀 Cài đặt

### Cách 1: Cài đặt thủ công

```bash
# Cài đặt Python packages
pip install playwright requests

# Cài đặt Chromium browser
python -m playwright install chromium
```

### Cách 2: Sử dụng file batch (Windows)

```bash
# Chạy file batch để tự động setup
run.bat
```

### Cách 3: <PERSON>ript setup tự động

```bash
python setup.py
```

## 📖 Cách sử dụng

### Sử dụng cơ bản

```bash
python run_crawler.py "Language school"
```

### Với các tùy chọn nâng cao

```bash
# Lấy 100 kết quả với thông tin chi tiết
python run_crawler.py "Restaurant" --max-results 100 --get-details

# Chạy ẩn browser
python run_crawler.py "Coffee shop" --headless

# Chỉ định file output
python run_crawler.py "Hotel" --output my_results.json
```

### Sử dụng trong code Python

```python
from google_maps_crawler import GoogleMapsCrawler

# Khởi tạo crawler
crawler = GoogleMapsCrawler(headless=True)

# Crawl dữ liệu
results = crawler.crawl_search_results(
    query="Language school",
    get_details=True,
    max_results=50
)

# Lưu kết quả
crawler.save_to_json("results.json")

# In tóm tắt
crawler.print_summary()
```

## 📊 Định dạng kết quả

Kết quả được xuất ra dưới dạng JSON với cấu trúc:

```json
[
  {
    "title": "Apollo English Centre",
    "phone": "+84 24 3974 4157",
    "website": "https://apollo.edu.vn",
    "email": "",
    "totalScore": 4.2,
    "reviewsCount": 324,
    "street": "127 Nguyễn Huệ",
    "city": "Quận Hai Bà Trưng",
    "state": "Hà Nội",
    "countryCode": "VN",
    "categoryName": "Trung tâm tiếng Anh",
    "url": "https://www.google.com/maps/place/ChIJAyXdEfFbNDERg8AiLELrVBz",
    "formatted_address": "127 Nguyễn Huệ, Quận Hai Bà Trưng, Hà Nội, Việt Nam",
    "place_id": "ChIJAyXdEfFbNDERg8AiLELrVBz"
  }
]
```

## ⚙️ Tùy chọn

| Tham số | Mô tả | Mặc định |
|---------|-------|----------|
| `query` | Từ khóa tìm kiếm | Bắt buộc |
| `--max-results` | Số kết quả tối đa | 50 |
| `--headless` | Chạy browser ẩn | False |
| `--get-details` | Lấy thông tin chi tiết | False |
| `--output` | Tên file output | Tự động |

## 🔧 Cấu hình nâng cao

Bạn có thể tùy chỉnh crawler trong code:

```python
crawler = GoogleMapsCrawler(
    headless=True,          # Chạy ẩn browser
    timeout=30000           # Timeout 30 giây
)
```

## 🚨 Lưu ý quan trọng

1. **Tuân thủ robots.txt**: Tool này chỉ dành cho mục đích học tập và nghiên cứu
2. **Rate limiting**: Có delay giữa các request để tránh bị block
3. **Selectors**: Google có thể thay đổi cấu trúc HTML, cần cập nhật selectors
4. **Legal**: Đảm bảo tuân thủ Terms of Service của Google

## 🐛 Xử lý lỗi

### Lỗi thường gặp

1. **Không tìm thấy kết quả**: Thử thay đổi từ khóa hoặc kiểm tra kết nối mạng
2. **Browser không khởi động**: Chạy `playwright install chromium`
3. **Timeout**: Tăng giá trị timeout hoặc kiểm tra tốc độ mạng

### Debug

Để debug, chạy với `headless=False` để xem browser:

```bash
python run_crawler.py "Language school" --max-results 10
```

## 📝 Changelog

- **v1.0**: Phiên bản đầu tiên với đầy đủ tính năng crawl Google Maps

## 🤝 Đóng góp

Mọi đóng góp đều được chào đón! Hãy tạo issue hoặc pull request.

## 📄 License

MIT License - Xem file LICENSE để biết thêm chi tiết.
