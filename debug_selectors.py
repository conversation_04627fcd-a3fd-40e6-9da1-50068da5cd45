#!/usr/bin/env python3
"""
Script debug để xem cấu trúc HTML của Google Maps
"""

from playwright.sync_api import sync_playwright
import time

def debug_google_maps_structure():
    """Debug cấu trúc HTML của Google Maps"""
    print("🔍 Debug cấu trúc HTML Google Maps...")
    
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        context = browser.new_context(
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        )
        page = context.new_page()
        
        try:
            # Truy cập Google Maps với tìm kiếm
            search_url = "https://www.google.com/maps/search/Coffee+shop/@14.0583,108.2772,6z?gl=vn&hl=vi"
            print(f"📍 Truy cập: {search_url}")
            
            page.goto(search_url, timeout=60000)
            time.sleep(5)
            
            # Tìm kết quả đầu tiên
            results = page.query_selector_all('a[href*="/place/"]')
            print(f"📊 Tìm thấy {len(results)} kết quả")
            
            if results:
                first_result = results[0]
                print("\n🔍 Phân tích kết quả đầu tiên:")
                print("-" * 50)
                
                # Lấy HTML của element
                html = first_result.inner_html()
                print("📄 HTML structure:")
                print(html[:500] + "..." if len(html) > 500 else html)
                
                print("\n🏷️  Các text elements:")
                # Tìm tất cả text elements
                text_elements = first_result.query_selector_all('*')
                for i, elem in enumerate(text_elements[:10]):  # Chỉ lấy 10 đầu
                    try:
                        text = elem.inner_text().strip()
                        if text and len(text) < 100:
                            tag = elem.tag_name
                            classes = elem.get_attribute('class') or ''
                            print(f"  {i+1}. <{tag} class='{classes[:50]}...'> {text}")
                    except:
                        pass
                
                print("\n🎯 Test các selector phổ biến:")
                
                # Test các selector
                selectors_to_test = [
                    ('Title', 'div.qBF1Pd'),
                    ('Title 2', 'div.NrDZNb'),
                    ('Title 3', 'span.fontHeadlineSmall'),
                    ('Address', 'span.W4Efsd'),
                    ('Address 2', 'div.fontBodyMedium'),
                    ('Rating', 'span.MW4etd'),
                    ('Rating 2', 'div[role="img"]'),
                    ('Reviews', 'span.UY7F9'),
                    ('Category', 'span.W4Efsd:first-child'),
                ]
                
                for name, selector in selectors_to_test:
                    try:
                        elem = first_result.query_selector(selector)
                        if elem:
                            text = elem.inner_text().strip()
                            print(f"  ✅ {name}: '{text}'")
                        else:
                            print(f"  ❌ {name}: Không tìm thấy")
                    except Exception as e:
                        print(f"  ⚠️  {name}: Lỗi - {e}")
                
                # Thử click vào kết quả để xem thông tin chi tiết
                print("\n🖱️  Click để xem thông tin chi tiết...")
                first_result.click()
                time.sleep(3)
                
                # Tìm thông tin chi tiết
                print("\n📋 Thông tin chi tiết:")
                detail_selectors = [
                    ('Phone', 'button[data-value="Phone number"]'),
                    ('Phone 2', 'button[aria-label*="phone"]'),
                    ('Website', 'a[data-value="Website"]'),
                    ('Website 2', 'a[aria-label*="Website"]'),
                    ('Address Detail', 'button[data-value="Address"]'),
                    ('Rating Detail', 'div.F7nice'),
                    ('Reviews Detail', 'button[aria-label*="reviews"]'),
                ]
                
                for name, selector in detail_selectors:
                    try:
                        elem = page.query_selector(selector)
                        if elem:
                            if 'aria-label' in selector:
                                text = elem.get_attribute('aria-label') or elem.inner_text().strip()
                            else:
                                text = elem.inner_text().strip()
                            print(f"  ✅ {name}: '{text}'")
                        else:
                            print(f"  ❌ {name}: Không tìm thấy")
                    except Exception as e:
                        print(f"  ⚠️  {name}: Lỗi - {e}")
                
                print("\n⏸️  Dừng để bạn có thể inspect thủ công...")
                input("Nhấn Enter để tiếp tục...")
                
        except Exception as e:
            print(f"❌ Lỗi: {e}")
            
        finally:
            browser.close()

if __name__ == "__main__":
    debug_google_maps_structure()
