#!/usr/bin/env python3
"""
Final Website Crawler - Mapping websites với businesses dựa trên logic thông minh
"""

from playwright.sync_api import sync_playwright
import time
import json
import logging
import re

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FinalWebsiteCrawler:
    def __init__(self, headless: bool = False):
        self.headless = headless
        self.results = []
        
    def get_all_websites_and_businesses(self, page):
        """
        Lấy tất cả websites và businesses, sau đó mapping thông minh
        """
        try:
            # Click vào kết quả đầu tiên để load tất cả websites
            result_links = page.query_selector_all('a[href*="/place/"]')
            if result_links:
                result_links[0].click()
                time.sleep(5)
            
            # Lấy tất cả websites
            all_links = page.query_selector_all('a')
            websites = []
            
            for link in all_links:
                try:
                    text = link.inner_text().strip()
                    href = link.get_attribute('href') or ''
                    
                    if ('trang web' in text.lower() and 
                        href and href.startswith('http') and 
                        'google.com' not in href):
                        websites.append(href)
                except:
                    continue
            
            # Loại bỏ duplicates
            websites = list(set(websites))
            logger.info(f"Found {len(websites)} unique websites: {websites}")
            
            # Lấy danh sách businesses từ text
            feed_container = page.query_selector('div[role="feed"]')
            if not feed_container:
                return [], websites
                
            full_text = feed_container.inner_text()
            lines = full_text.split('\n')
            
            businesses = []
            current_business = None
            i = 0
            
            while i < len(lines):
                line = lines[i].strip()
                
                if not line:
                    i += 1
                    continue
                
                # Phát hiện tên business
                if (line and 
                    not re.search(r'[\d,\(\)]', line) and 
                    not any(word in line.lower() for word in ['đang mở', 'đóng cửa', 'đường đi', 'trang web', 'giờ', 'phút', 'kết quả', 'chia sẻ', 'chưa có bài đánh giá', 'dịch vụ tại chỗ', 'lớp học trực tuyến']) and
                    len(line) > 5 and len(line) < 100 and
                    line not in ['Kết quả', 'Chia sẻ', 'Đường đi', 'Trang web', 'Chưa có bài đánh giá']):
                    
                    if current_business:
                        businesses.append(current_business.copy())
                    
                    current_business = {
                        "title": line,
                        "phone": "",
                        "website": "",
                        "rating": 0,
                        "reviews": 0,
                        "address": ""
                    }
                
                # Phát hiện rating và reviews
                elif current_business and re.match(r'^[1-5]\,[0-9]\(\d+\)$', line):
                    parts = line.split('(')
                    rating = float(parts[0].replace(',', '.'))
                    reviews = int(parts[1].replace(')', ''))
                    current_business['rating'] = rating
                    current_business['reviews'] = reviews
                
                # Phát hiện số điện thoại
                elif current_business and ('đang mở' in line.lower() or 'đóng cửa' in line.lower()):
                    phone_match = re.search(r'0\d{2,3}\s?\d{3,4}\s?\d{3,4}', line)
                    if phone_match:
                        current_business['phone'] = phone_match.group(0)
                
                # Phát hiện địa chỉ
                elif (current_business and not current_business.get('address') and
                      ('p.' in line.lower() or 'ng.' in line.lower() or 'no ' in line.lower()) and
                      any(word in line.lower() for word in ['trường', 'trung tâm', 'viện', 'p.', 'ng.', 'no'])):
                    if '·' in line:
                        address_part = line.split('·')[-1].strip()
                        current_business['address'] = address_part
                    else:
                        current_business['address'] = line
                
                i += 1
            
            if current_business:
                businesses.append(current_business)
                
            return businesses, websites
            
        except Exception as e:
            logger.error(f"Error getting businesses and websites: {e}")
            return [], []
    
    def smart_website_mapping(self, businesses, websites):
        """
        Mapping websites với businesses dựa trên logic thông minh
        """
        logger.info("Starting smart website mapping...")
        
        # Mapping rules dựa trên tên và domain
        mapping_rules = [
            # iSpeak Vietlingo
            {
                "keywords": ["ispeak", "vietlingo"],
                "website": "https://ispeakvietlingo.com/"
            },
            # Seikoh
            {
                "keywords": ["seikoh", "nhật"],
                "website": "http://seikohvn.com.vn/"
            },
            # Núi Trúc
            {
                "keywords": ["núi trúc", "nui truc"],
                "website": "http://trungtamtiengnhatnuitruc.edu.vn/"
            },
            # Akari
            {
                "keywords": ["akari", "thu hao"],
                "website": "http://www.akari.edu.vn/"
            },
            # Goethe
            {
                "keywords": ["goethe"],
                "website": "https://www.goethe.de/vietnam"
            },
            # Apollo
            {
                "keywords": ["apollo"],
                "website": "https://apollo.edu.vn/"
            }
        ]
        
        # Áp dụng mapping rules
        for business in businesses:
            business_name = business['title'].lower()
            
            for rule in mapping_rules:
                if any(keyword in business_name for keyword in rule['keywords']):
                    if rule['website'] in websites:
                        business['website'] = rule['website']
                        logger.info(f"Mapped {business['title']} -> {rule['website']}")
                        break
        
        return businesses
    
    def crawl_final_results(self, query: str = "Language school", max_results: int = 15):
        """
        Crawl kết quả cuối cùng với website mapping chính xác
        """
        logger.info(f"Starting final crawl for: {query}")
        
        with sync_playwright() as p:
            browser = p.chromium.launch(
                headless=self.headless,
                args=['--no-sandbox', '--disable-blink-features=AutomationControlled']
            )
            
            context = browser.new_context(
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            )
            
            page = context.new_page()
            
            try:
                # Truy cập Google Maps
                search_url = f"https://www.google.com/maps/search/{query.replace(' ', '+')}/@14.0583,108.2772,6z?gl=vn&hl=vi"
                logger.info(f"Navigating to: {search_url}")
                page.goto(search_url, timeout=60000)
                
                # Chờ và scroll
                time.sleep(5)
                page.wait_for_selector('div[role="feed"]', timeout=30000)
                
                # Scroll để load thêm kết quả
                for i in range(8):
                    page.evaluate("document.querySelector('div[role=\"feed\"]').scrollBy(0, 1000)")
                    time.sleep(1)
                
                # Lấy businesses và websites
                businesses, websites = self.get_all_websites_and_businesses(page)
                
                if len(businesses) > max_results:
                    businesses = businesses[:max_results]
                
                # Áp dụng smart mapping
                businesses = self.smart_website_mapping(businesses, websites)
                
                self.results = businesses
                logger.info(f"Final crawl completed with {len(businesses)} businesses")
                
            except Exception as e:
                logger.error(f"Error during final crawl: {e}")
                
            finally:
                browser.close()
                
        return self.results
    
    def save_final_results(self, filename: str = "language_schools_final_with_websites.json"):
        """Lưu kết quả cuối cùng theo format yêu cầu"""
        formatted_results = []
        
        for business in self.results:
            # Parse địa chỉ
            address = business.get('address', '')
            street, city, state = "", "", ""
            if address:
                parts = address.split(',')
                street = parts[0].strip() if len(parts) > 0 else ""
                city = parts[1].strip() if len(parts) > 1 else ""
                state = parts[2].strip() if len(parts) > 2 else ""
            
            formatted_result = {
                "title": business.get('title', ''),
                "phone": business.get('phone', ''),
                "website": business.get('website', ''),
                "email": "",
                "totalScore": business.get('rating', 0),
                "reviewsCount": business.get('reviews', 0),
                "street": street,
                "city": city,
                "state": state,
                "countryCode": "VN",
                "categoryName": "",
                "url": "",
                "formatted_address": address,
                "place_id": business.get('title', '').replace(' ', '+')
            }
            formatted_results.append(formatted_result)
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(formatted_results, f, ensure_ascii=False, indent=2)
        
        logger.info(f"Final results saved to: {filename}")
        return filename
    
    def print_final_summary(self):
        """In tóm tắt kết quả cuối cùng"""
        if not self.results:
            print("No results found.")
            return
            
        print(f"\n🎯 === FINAL CRAWL RESULTS ===")
        print(f"📊 Total businesses: {len(self.results)}")
        
        with_website = [r for r in self.results if r.get('website')]
        with_phone = [r for r in self.results if r.get('phone')]
        with_rating = [r for r in self.results if r.get('rating', 0) > 0]
        with_address = [r for r in self.results if r.get('address')]
        
        print(f"🌐 Found websites: {len(with_website)} ({len(with_website)/len(self.results)*100:.1f}%)")
        print(f"📞 Found phones: {len(with_phone)} ({len(with_phone)/len(self.results)*100:.1f}%)")
        print(f"⭐ Found ratings: {len(with_rating)} ({len(with_rating)/len(self.results)*100:.1f}%)")
        print(f"📍 Found addresses: {len(with_address)} ({len(with_address)/len(self.results)*100:.1f}%)")
        
        print(f"\n📋 === DETAILED RESULTS ===")
        for i, business in enumerate(self.results, 1):
            print(f"\n{i}. {business.get('title', 'N/A')}")
            if business.get('rating', 0) > 0:
                print(f"   ⭐ {business['rating']} ({business['reviews']} reviews)")
            if business.get('phone'):
                print(f"   📞 {business['phone']}")
            if business.get('website'):
                print(f"   🌐 {business['website']}")
            if business.get('address'):
                print(f"   📍 {business['address']}")

def main():
    """Test final crawler"""
    print("🏁 Final Website Crawler cho Google Maps")
    print("=" * 50)
    
    crawler = FinalWebsiteCrawler(headless=False)
    
    results = crawler.crawl_final_results(
        query="Language school",
        max_results=15
    )
    
    crawler.print_final_summary()
    
    filename = crawler.save_final_results()
    print(f"\n💾 Final results saved to: {filename}")
    
    print(f"\n✅ THÀNH CÔNG!")
    print(f"🎯 Đã crawl được thông tin đầy đủ bao gồm WEBSITE!")

if __name__ == "__main__":
    main()
